{"time": "2025-07-17 22:10:38.769591", "level": "INFO", "message": "learner train process start at pid is 216", "file": "trainer.py", "line": "32", "module": "learner", "process": "trainer", "function": "__init__", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:38.836262", "level": "INFO", "message": "learner train replaybuff, use reverb", "file": "replay_buffer_wrapper.py", "line": "58", "module": "learner", "process": "replay_buffer_wrapper", "function": "__init__", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:38.862687", "level": "INFO", "message": "learner model_file_save process start, type is 0, no need get mode file from cos", "file": "model_file_save.py", "line": "776", "module": "learner", "process": "model_file_save", "function": "start_actor_process_by_type", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:38.889466", "level": "INFO", "message": "learner monitor_proxy process start success at pid is 289", "file": "monitor_proxy_process.py", "line": "73", "module": "learner", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 289}
{"time": "2025-07-17 22:10:39.566223", "level": "INFO", "message": "learner policy_name train_one, algo diy, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "learner", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:39.587132", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 291}
{"time": "2025-07-17 22:10:39.588763", "level": "INFO", "message": "model_file_sync process pid is 291", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 291}
{"time": "2025-07-17 22:10:39.590076", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 291}
{"time": "2025-07-17 22:10:39.591792", "level": "INFO", "message": "learner ppid is 140681992238912", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 291}
{"time": "2025-07-17 22:10:39.591479", "level": "INFO", "message": "learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv", "file": "on_policy_trainer.py", "line": "674", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:39.597850", "level": "INFO", "message": "learner ppid is 140681992238912", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 292}
{"time": "2025-07-17 22:10:39.600137", "level": "INFO", "message": "learner save model /data/ckpt/gorge_walk_v2_diy/model.ckpt-0.pth successfully", "file": "agent.py", "line": "189", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:39.600569", "level": "INFO", "message": "model_file_save process start success at pid 292", "file": "model_file_save.py", "line": "300", "module": "learner", "process": "model_file_save", "function": "before_run", "stack": "", "pid": 292}
{"time": "2025-07-17 22:10:39.632644", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/gorge_walk_v2_diy/kaiwu_checkpoint_gorge_walk_v2_diy_0.tar.gz key model.ckpt_gorge_walk_v2_diy_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:39.634047", "level": "INFO", "message": "learner train first model file push to modelpool success", "file": "on_policy_trainer.py", "line": "706", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:39.635332", "level": "INFO", "message": "learner train process start success at 216, on-policy/off-policy is off-policy, diy trainer global step -1.0, load app gorge_walk_v2 algo diy model, train_batch_size is 256", "file": "on_policy_trainer.py", "line": "739", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 216}
{"time": "2025-07-17 22:10:39.639580", "level": "INFO", "message": "learner start_background_filler success, reverb.Client connect at localhost:9999", "file": "reverb_dataset_v1.py", "line": "56", "module": "learner", "process": "reverb_dataset_v1", "function": "start_background_filler", "stack": "", "pid": 216}
{"time": "2025-07-17 22:12:39.671002", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/gorge_walk_v2_diy/kaiwu_checkpoint_gorge_walk_v2_diy_0.tar.gz key model.ckpt_gorge_walk_v2_diy_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 291}
