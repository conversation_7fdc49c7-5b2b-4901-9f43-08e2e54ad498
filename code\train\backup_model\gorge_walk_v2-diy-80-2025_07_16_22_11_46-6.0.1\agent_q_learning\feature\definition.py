#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import numpy as np
from kaiwu_agent.utils.common_func import create_cls, attached


SampleData = create_cls("SampleData", state=None, action=None, reward=None, next_state=None, done=None)


@attached
def sample_process(list_game_data):
    return [SampleData(**i.__dict__) for i in list_game_data]


def reward_shaping(frame_no, score, terminated, truncated, obs, _obs):
    """
    Enhanced reward shaping for Q-learning algorithm
    为Q-learning算法设计的增强奖励塑形函数
    """
    reward = 0
    end_treasure_dists = obs["feature"]
    _end_treasure_dists = _obs["feature"]

    # Reward 1: Victory reward (large positive reward for reaching the goal)
    # 奖励1. 获胜的奖励 (到达目标的大正奖励)
    if terminated:
        reward += 100  # Large reward for completing the task
        if score > 0:
            reward += score * 10  # Bonus for collecting treasures

    # Reward 2: Progress toward the finish line
    # 奖励2. 靠近终点的奖励
    if len(end_treasure_dists) > 0 and len(_end_treasure_dists) > 0:
        end_dist, _end_dist = end_treasure_dists[0], _end_treasure_dists[0]
        if end_dist > _end_dist:
            reward += 1.0  # Reward for getting closer to the goal

    # Reward 3: Treasure collection reward
    # 奖励3. 获得宝箱的奖励
    if score > 0 and not terminated:
        reward += score * 5  # Reward for collecting treasures

    # Reward 4: Progress toward treasure chests
    # 奖励4. 靠近宝箱的奖励
    if len(end_treasure_dists) > 1 and len(_end_treasure_dists) > 1:
        treasure_dist, _treasure_dist = end_treasure_dists[1:], _end_treasure_dists[1:]
        if len(treasure_dist) > 0 and len(_treasure_dist) > 0:
            nearest_treasure_index = np.argmin(treasure_dist)
            if treasure_dist[nearest_treasure_index] > _treasure_dist[nearest_treasure_index]:
                reward += 0.5  # Small reward for approaching treasures

    # Penalty 1: Time penalty to encourage efficiency
    # 惩罚1. 时间惩罚，鼓励效率
    reward -= 0.01  # Small time penalty to encourage faster completion

    # Penalty 2: Truncation penalty (timeout)
    # 惩罚2. 超时惩罚
    if truncated:
        reward -= 10  # Penalty for running out of time

    return reward
