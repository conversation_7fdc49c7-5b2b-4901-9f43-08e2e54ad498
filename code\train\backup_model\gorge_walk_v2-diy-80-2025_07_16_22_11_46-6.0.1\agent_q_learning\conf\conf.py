#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


# Configuration of dimensions
# 关于维度的配置
class Config:
    """
    Q-learning algorithm configuration
    Q-learning算法配置
    """

    # Environment parameters
    # 环境参数
    STATE_SIZE = 64 * 64 * 1024  # Total possible states
    ACTION_SIZE = 4              # Number of actions (up, down, left, right)

    # Q-learning hyperparameters
    # Q-learning超参数
    LEARNING_RATE = 0.1          # Learning rate (alpha) - reduced for more stable learning
    GAMMA = 0.95                 # Discount factor - increased to value future rewards more
    EPSILON = 0.1                # Initial exploration rate for epsilon-greedy
    EPISODES = 10000             # Maximum training episodes

    # Model configuration
    # 模型配置
    USE_DQN = False              # Whether to use Deep Q-Network (True) or Q-table (False)
                                 # 是否使用深度Q网络（True）或Q表（False）
                                 # Note: DQN requires PyTorch installation
                                 # 注意：DQN需要安装PyTorch
    DQN_LEARNING_RATE = 0.001    # Learning rate for DQN optimizer
    DQN_BUFFER_SIZE = 10000      # Experience replay buffer size
    DQN_BATCH_SIZE = 32          # Training batch size for DQN
    DQN_TARGET_UPDATE = 100      # Target network update frequency

    # Sample and observation dimensions
    # 样本和观察维度
    SAMPLE_DIM = 5               # Sample dimension for Q-learning (state, action, reward, next_state, done)
    OBSERVATION_SHAPE = 250      # Observation feature dimension
