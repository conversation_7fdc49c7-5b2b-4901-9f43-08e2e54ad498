{"time": "2025-07-17 22:10:39.587132", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 291}
{"time": "2025-07-17 22:10:39.588763", "level": "INFO", "message": "model_file_sync process pid is 291", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 291}
{"time": "2025-07-17 22:10:39.590076", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 291}
{"time": "2025-07-17 22:12:39.671002", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/gorge_walk_v2_diy/kaiwu_checkpoint_gorge_walk_v2_diy_0.tar.gz key model.ckpt_gorge_walk_v2_diy_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 291}
