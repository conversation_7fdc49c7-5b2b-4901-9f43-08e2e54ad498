{"time": "2025-07-16 09:24:01.387653", "level": "INFO", "message": "learner train process start at pid is 64181", "file": "trainer.py", "line": "32", "module": "learner", "process": "trainer", "function": "__init__", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:01.471066", "level": "INFO", "message": "learner train replaybuff, use reverb", "file": "replay_buffer_wrapper.py", "line": "58", "module": "learner", "process": "replay_buffer_wrapper", "function": "__init__", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:01.561574", "level": "INFO", "message": "learner model_file_save process start, type is 0, no need get mode file from cos", "file": "model_file_save.py", "line": "776", "module": "learner", "process": "model_file_save", "function": "start_actor_process_by_type", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:02.090726", "level": "INFO", "message": "learner monitor_proxy process start success at pid is 64636", "file": "monitor_proxy_process.py", "line": "73", "module": "learner", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 64636}
{"time": "2025-07-16 09:24:02.979195", "level": "INFO", "message": "learner policy_name train_one, algo q_learning, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "learner", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:03.050948", "level": "INFO", "message": "learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv", "file": "on_policy_trainer.py", "line": "674", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:03.358493", "level": "INFO", "message": "learner save model /data/ckpt/gorge_walk_v2_q_learning/model.ckpt-0.npy successfully", "file": "agent.py", "line": "219", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:04.466118", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/gorge_walk_v2_q_learning/kaiwu_checkpoint_gorge_walk_v2_q_learning_0.tar.gz key model.ckpt_gorge_walk_v2_q_learning_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:04.469058", "level": "INFO", "message": "learner train first model file push to modelpool success", "file": "on_policy_trainer.py", "line": "706", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:04.476567", "level": "INFO", "message": "learner train process start success at 64181, on-policy/off-policy is off-policy, q_learning trainer global step -1.0, load app gorge_walk_v2 algo q_learning model, train_batch_size is 2", "file": "on_policy_trainer.py", "line": "739", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:04.518419", "level": "INFO", "message": "learner start_background_filler success, reverb.Client connect at localhost:9999", "file": "reverb_dataset_v1.py", "line": "56", "module": "learner", "process": "reverb_dataset_v1", "function": "start_background_filler", "stack": "", "pid": 64181}
{"time": "2025-07-16 09:24:10.239030", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 64717}
{"time": "2025-07-16 09:24:10.369482", "level": "INFO", "message": "model_file_sync process pid is 64717", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 64717}
{"time": "2025-07-16 09:24:10.451812", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_q_learning/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 64717}
{"time": "2025-07-16 09:24:10.486013", "level": "INFO", "message": "learner ppid is 132213906208576", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 64717}
{"time": "2025-07-16 09:24:14.158287", "level": "INFO", "message": "learner ppid is 132213906208576", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 64720}
{"time": "2025-07-16 09:24:14.214624", "level": "INFO", "message": "model_file_save process start success at pid 64720", "file": "model_file_save.py", "line": "300", "module": "learner", "process": "model_file_save", "function": "before_run", "stack": "", "pid": 64720}
