{"time": "2025-07-17 22:10:44.646548", "level": "INFO", "message": "aisrv kaiwu_rl_helper start at pid 331, ppid is 129334811309888, thread id is -1", "file": "kaiwu_rl_helper_standard.py", "line": "134", "module": "aisrv", "process": "kaiwu_rl_helper_standard", "function": "__init__", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:44.649745", "level": "INFO", "message": "aisrv aisrvhandle use kaiwu_rl_helper: <KaiWuRLStandardHelper(kaiwu_rl_helper_0, initial daemon)>", "file": "aisrv_server_standard.py", "line": "794", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:44.652108", "level": "INFO", "message": "aisrv aisrvhandle established connect to 172.20.0.6:5566, slot id is 0, min_slot_id is 0", "file": "aisrv_server_standard.py", "line": "797", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:44.654435", "level": "INFO", "message": "aisrv aisrvhandle current_actor_addrs is ['127.0.0.1:8888'], current_learner_addrs is ['127.0.0.1:9999']", "file": "aisrv_server_standard.py", "line": "806", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:44.656633", "level": "INFO", "message": "aisrv aisrvhandle start success at pid 331", "file": "aisrv_server_standard.py", "line": "837", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:44.966981", "level": "INFO", "message": "aisrv policy_name train_one, algo diy, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "aisrv", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:44.973782", "level": "INFO", "message": "aisrv save model /data/ckpt/gorge_walk_v2_diy/model.ckpt-0.pth successfully", "file": "agent.py", "line": "189", "module": "aisrv", "process": "agent", "function": "save_model", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:45.001816", "level": "INFO", "message": "aisrv env proxy, zmq_client connect to 172.20.0.6:5566 success, client_id 33917097", "file": "env_proxy_lazy.py", "line": "54", "module": "aisrv", "process": "env_proxy_lazy", "function": "__init__", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:45.003504", "level": "INFO", "message": "aisrv kaiwu_rl_helper start agent 0 with train_one", "file": "kaiwu_rl_helper_standard.py", "line": "1017", "module": "aisrv", "process": "kaiwu_rl_helper_standard", "function": "start_agent", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:45.009141", "level": "INFO", "message": "aisrv Start Training...", "file": "train_workflow.py", "line": "78", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:45.017403", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 0, 'sample_production_and_consumption_ratio': 0, 'predict_succ_cnt': 0}, 'algorithm': {'reward': 0}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'total_treasures': 0, 'collected_treasures': 0, 'treasure_random': 0}, 'diy': {'diy_1': 0, 'diy_2': 0, 'diy_3': 0, 'diy_4': 0, 'diy_5': 0}}", "file": "train_workflow.py", "line": "101", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:10:45.279400", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:11:05.342250", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:11:22.914676", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 1.0, 'sample_production_and_consumption_ratio': 0, 'predict_succ_cnt': 0}, 'algorithm': {'reward': 0}, 'env': {'total_score': 0.0, 'treasure_score': 300.0, 'max_steps': 2000.0, 'finished_steps': 2000.0, 'total_treasures': 5.0, 'collected_treasures': 3.0, 'treasure_random': 0.0}, 'diy': {'diy_1': 0, 'diy_2': 0, 'diy_3': 0, 'diy_4': 0, 'diy_5': 0}}", "file": "train_workflow.py", "line": "101", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:11:22.924740", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:11:39.770643", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:12:03.575944", "level": "INFO", "message": "aisrv Episode: 5, Avg Reward: -296.72500000000446, Current Loss: 4.982544", "file": "train_workflow.py", "line": "211", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:12:03.576952", "level": "INFO", "message": "aisrv Training Win Rate: 0.0, 0, 5", "file": "train_workflow.py", "line": "212", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:12:03.584038", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 1.0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 1.0, 'sample_production_and_consumption_ratio': 0.0, 'predict_succ_cnt': 4002.0}, 'algorithm': {'reward': 0}, 'env': {'total_score': 0.0, 'treasure_score': 300.0, 'max_steps': 2000.0, 'finished_steps': 2000.0, 'total_treasures': 5.0, 'collected_treasures': 3.0, 'treasure_random': 0.0}, 'diy': {'diy_1': 0, 'diy_2': 0, 'diy_3': 0, 'diy_4': 0, 'diy_5': 0}}", "file": "train_workflow.py", "line": "101", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:12:03.597439", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:12:24.972913", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:12:53.083251", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 1.0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 5.0, 'sample_production_and_consumption_ratio': 0.0, 'predict_succ_cnt': 4002.0}, 'algorithm': {'reward': -296.73}, 'env': {'total_score': 0.0, 'treasure_score': 125.0, 'max_steps': 2000.0, 'finished_steps': 2000.0, 'total_treasures': 5.0, 'collected_treasures': 1.25, 'treasure_random': 0.0}, 'diy': {'diy_1': 0.0, 'diy_2': 4002.0, 'diy_3': 4.98, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "101", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:12:53.093578", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:13:15.717068", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:13:32.837023", "level": "INFO", "message": "aisrv Episode: 9, Avg Reward: -226.5624999999972, Current Loss: 4.187481", "file": "train_workflow.py", "line": "211", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:13:32.837807", "level": "INFO", "message": "aisrv Training Win Rate: 0.0, 0, 9", "file": "train_workflow.py", "line": "212", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:13:32.840311", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 3.0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 5.0, 'sample_production_and_consumption_ratio': 0.0, 'predict_succ_cnt': 12006.0}, 'algorithm': {'reward': -296.73}, 'env': {'total_score': 0.0, 'treasure_score': 125.0, 'max_steps': 2000.0, 'finished_steps': 2000.0, 'total_treasures': 5.0, 'collected_treasures': 1.25, 'treasure_random': 0.0}, 'diy': {'diy_1': 0.0, 'diy_2': 4002.0, 'diy_3': 4.98, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "101", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 331}
{"time": "2025-07-17 22:13:32.849401", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
{"time": "2025-07-17 22:13:49.650067", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "87", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 331}
