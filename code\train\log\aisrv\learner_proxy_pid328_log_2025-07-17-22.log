{"time": "2025-07-17 22:10:46.541073", "level": "INFO", "message": "aisrv learner_proxy reverb client Client, server_address=127.0.0.1:9999 127.0.0.1:9999 connect to reverb server", "file": "reverb_util.py", "line": "22", "module": "aisrv", "process": "reverb_util", "function": "__init__", "stack": "", "pid": 328}
{"time": "2025-07-17 22:10:46.542903", "level": "INFO", "message": "learner_proxy send reverb server use reverb, tables is ['reverb_replay_buffer_table_0']", "file": "learner_proxy.py", "line": "118", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 328}
{"time": "2025-07-17 22:10:46.545960", "level": "INFO", "message": "learner_proxy zmq client connect at 127.0.0.1:9997 with client_id 57563049", "file": "learner_proxy.py", "line": "155", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 328}
{"time": "2025-07-17 22:10:46.565679", "level": "INFO", "message": "learner_proxy policy_name: train_one, start success at pid 328", "file": "learner_proxy.py", "line": "174", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 328}
