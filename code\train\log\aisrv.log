2025-07-17 22:10:44.573 | INFO | PID:325 | model_file_sync.before_run:189 - aisrv model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch
2025-07-17 22:10:44.575 | INFO | PID:325 | model_file_sync.before_run:195 - model_file_sync process pid is 325
2025-07-17 22:10:44.576 | INFO | PID:325 | model_file_sync.make_model_dirs:138 - model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/plugins success
2025-07-17 22:10:44.577 | INFO | PID:325 | model_file_sync.make_model_dirs:144 - model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/models /data/ckpt/gorge_walk_v2_diy/plugins success
2025-07-17 22:10:44.578 | INFO | PID:325 | model_file_sync.make_model_dirs:153 - model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/convert_models_aisrv success
2025-07-17 22:10:44.580 | INFO | PID:325 | monitor_proxy.__init__:36 - aisrv ppid is 129334811309888
2025-07-17 22:10:44.622 | INFO | PID:306 | aisrv_server_standard.start_aisrv_handler:484 - aisrv AiServer AiSrvHandle with address: **********:5566 start
2025-07-17 22:10:44.624 | INFO | PID:329 | monitor_proxy_process.before_run:73 - aisrv monitor_proxy process start success at pid is 329
2025-07-17 22:10:44.626 | INFO | PID:306 | aisrv_server_standard.before_run:604 - aisrv AiServer is start success at 0.0.0.0:8000, pid is 306, run_mode is train, self_play is False
2025-07-17 22:10:44.629 | INFO | PID:330 | monitor_proxy_process.before_run:73 - aisrv monitor_proxy process start success at pid is 330
2025-07-17 22:10:44.646 | INFO | PID:331 | kaiwu_rl_helper_standard.__init__:134 - aisrv kaiwu_rl_helper start at pid 331, ppid is 129334811309888, thread id is -1
2025-07-17 22:10:44.649 | INFO | PID:331 | aisrv_server_standard.before_run:794 - aisrv aisrvhandle use kaiwu_rl_helper: <KaiWuRLStandardHelper(kaiwu_rl_helper_0, initial daemon)>
2025-07-17 22:10:44.652 | INFO | PID:331 | aisrv_server_standard.before_run:797 - aisrv aisrvhandle established connect to **********:5566, slot id is 0, min_slot_id is 0
2025-07-17 22:10:44.654 | INFO | PID:331 | aisrv_server_standard.before_run:806 - aisrv aisrvhandle current_actor_addrs is ['127.0.0.1:8888'], current_learner_addrs is ['127.0.0.1:9999']
2025-07-17 22:10:44.656 | INFO | PID:331 | aisrv_server_standard.before_run:837 - aisrv aisrvhandle start success at pid 331
2025-07-17 22:10:44.827287: I tensorflow/core/util/port.cc:110] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-07-17 22:10:44.891993: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-07-17 22:10:44.942 | INFO | PID:327 | model_wrapper_common.create_standard_model_wrapper:99 - aisrv policy_name train_one, algo diy, model_wrapper is StandardModelWrapperPytorch
2025-07-17 22:10:44.945 | INFO | PID:327 | multi_model_common.init_load_models:240 - aisrv predict just no model_pool files or copy_model_files failed, please check
2025-07-17 22:10:44.946 | INFO | PID:327 | actor_proxy_local.before_run:663 - predict policy_name: train_one, start success at pid 327, on-policy/off-policy is off-policy, actor_receive_cost_time_ms: 1, predict_batch_size: 1
2025-07-17 22:10:44.947 | INFO | PID:327 | actor_proxy_local.predict_stat:482 - aisrv predict now predict count is 0
2025-07-17 22:10:44.966 | INFO | PID:331 | model_wrapper_common.create_standard_model_wrapper:99 - aisrv policy_name train_one, algo diy, model_wrapper is StandardModelWrapperPytorch
2025-07-17 22:10:44.973 | INFO | PID:331 | agent.save_model:189 - aisrv save model /data/ckpt/gorge_walk_v2_diy/model.ckpt-0.pth successfully
2025-07-17 22:10:45.001 | INFO | PID:331 | env_proxy_lazy.__init__:54 - aisrv env proxy, zmq_client connect to **********:5566 success, client_id 33917097
2025-07-17 22:10:45.003 | INFO | PID:331 | kaiwu_rl_helper_standard.start_agent:1017 - aisrv kaiwu_rl_helper start agent 0 with train_one
2025-07-17 22:10:45.009 | INFO | PID:331 | train_workflow.workflow:78 - aisrv Start Training...
2025-07-17 22:10:45.017 | INFO | PID:331 | train_workflow.workflow:101 - aisrv training_metrics is {'basic': {'train_global_step': 0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 0, 'sample_production_and_consumption_ratio': 0, 'predict_succ_cnt': 0}, 'algorithm': {'reward': 0}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'total_treasures': 0, 'collected_treasures': 0, 'treasure_random': 0}, 'diy': {'diy_1': 0, 'diy_2': 0, 'diy_3': 0, 'diy_4': 0, 'diy_5': 0}}
2025-07-17 22:10:45.279 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:10:45.972301: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT
2025-07-17 22:10:46.541 | INFO | PID:328 | reverb_util.__init__:22 - aisrv learner_proxy reverb client Client, server_address=127.0.0.1:9999 127.0.0.1:9999 connect to reverb server
2025-07-17 22:10:46.542 | INFO | PID:328 | learner_proxy.before_run:118 - learner_proxy send reverb server use reverb, tables is ['reverb_replay_buffer_table_0']
2025-07-17 22:10:46.545 | INFO | PID:328 | learner_proxy.before_run:155 - learner_proxy zmq client connect at 127.0.0.1:9997 with client_id 57563049
2025-07-17 22:10:46.565 | INFO | PID:328 | learner_proxy.before_run:174 - learner_proxy policy_name: train_one, start success at pid 328
2025-07-17 22:10:46.579 | INFO | PID:348 | monitor_proxy_process.before_run:73 - aisrv monitor_proxy process start success at pid is 348
2025-07-17 22:11:05.342 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
/data/projects/gorge_walk_v2/agent_diy/algorithm/algorithm.py:71: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at ../torch/csrc/utils/tensor_new.cpp:245.)
  all_states = torch.FloatTensor(states).to(self.device)
/data/projects/gorge_walk_v2/agent_diy/algorithm/algorithm.py:109: UserWarning: Using a target size (torch.Size([128])) that is different to the input size (torch.Size([128, 1])). This will likely lead to incorrect results due to broadcasting. Please ensure they have the same size.
  value_loss = F.mse_loss(value_preds, returns)  #
/data/projects/gorge_walk_v2/agent_diy/algorithm/algorithm.py:109: UserWarning: Using a target size (torch.Size([34])) that is different to the input size (torch.Size([34, 1])). This will likely lead to incorrect results due to broadcasting. Please ensure they have the same size.
  value_loss = F.mse_loss(value_preds, returns)  #
2025-07-17 22:11:22.914 | INFO | PID:331 | train_workflow.workflow:101 - aisrv training_metrics is {'basic': {'train_global_step': 0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 1.0, 'sample_production_and_consumption_ratio': 0, 'predict_succ_cnt': 0}, 'algorithm': {'reward': 0}, 'env': {'total_score': 0.0, 'treasure_score': 300.0, 'max_steps': 2000.0, 'finished_steps': 2000.0, 'total_treasures': 5.0, 'collected_treasures': 3.0, 'treasure_random': 0.0}, 'diy': {'diy_1': 0, 'diy_2': 0, 'diy_3': 0, 'diy_4': 0, 'diy_5': 0}}
2025-07-17 22:11:22.924 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:11:39.770 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:12:03.575 | INFO | PID:331 | train_workflow.workflow:211 - aisrv Episode: 5, Avg Reward: -296.72500000000446, Current Loss: 4.982544
2025-07-17 22:12:03.576 | INFO | PID:331 | train_workflow.workflow:212 - aisrv Training Win Rate: 0.0, 0, 5
2025-07-17 22:12:03.584 | INFO | PID:331 | train_workflow.workflow:101 - aisrv training_metrics is {'basic': {'train_global_step': 1.0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 1.0, 'sample_production_and_consumption_ratio': 0.0, 'predict_succ_cnt': 4002.0}, 'algorithm': {'reward': 0}, 'env': {'total_score': 0.0, 'treasure_score': 300.0, 'max_steps': 2000.0, 'finished_steps': 2000.0, 'total_treasures': 5.0, 'collected_treasures': 3.0, 'treasure_random': 0.0}, 'diy': {'diy_1': 0, 'diy_2': 0, 'diy_3': 0, 'diy_4': 0, 'diy_5': 0}}
2025-07-17 22:12:03.597 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:12:24.972 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:12:44.633 | INFO | PID:325 | model_file_sync.pull_checkpoint_from_model_pool:902 - model_file_sync pull kaiwu_checkpoint_gorge_walk_v2_diy_0.tar.gz from modelpool to /data/ckpt/gorge_walk_v2_diy/models/model.ckpt_gorge_walk_v2_diy_0 success                                 total pull from modelpool succ cnt is 1                                 total pull from modelpool err cnt is 0
2025-07-17 22:12:53.083 | INFO | PID:331 | train_workflow.workflow:101 - aisrv training_metrics is {'basic': {'train_global_step': 1.0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 5.0, 'sample_production_and_consumption_ratio': 0.0, 'predict_succ_cnt': 4002.0}, 'algorithm': {'reward': -296.73}, 'env': {'total_score': 0.0, 'treasure_score': 125.0, 'max_steps': 2000.0, 'finished_steps': 2000.0, 'total_treasures': 5.0, 'collected_treasures': 1.25, 'treasure_random': 0.0}, 'diy': {'diy_1': 0.0, 'diy_2': 4002.0, 'diy_3': 4.98, 'diy_4': 0.0, 'diy_5': 0.0}}
2025-07-17 22:12:53.093 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:13:15.717 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:13:32.837 | INFO | PID:331 | train_workflow.workflow:211 - aisrv Episode: 9, Avg Reward: -226.5624999999972, Current Loss: 4.187481
2025-07-17 22:13:32.837 | INFO | PID:331 | train_workflow.workflow:212 - aisrv Training Win Rate: 0.0, 0, 9
2025-07-17 22:13:32.840 | INFO | PID:331 | train_workflow.workflow:101 - aisrv training_metrics is {'basic': {'train_global_step': 3.0, 'load_model_succ_cnt': 0, 'sample_receive_cnt': 0, 'episode_cnt': 5.0, 'sample_production_and_consumption_ratio': 0.0, 'predict_succ_cnt': 12006.0}, 'algorithm': {'reward': -296.73}, 'env': {'total_score': 0.0, 'treasure_score': 125.0, 'max_steps': 2000.0, 'finished_steps': 2000.0, 'total_treasures': 5.0, 'collected_treasures': 1.25, 'treasure_random': 0.0}, 'diy': {'diy_1': 0.0, 'diy_2': 4002.0, 'diy_3': 4.98, 'diy_4': 0.0, 'diy_5': 0.0}}
2025-07-17 22:13:32.849 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:13:49.650 | INFO | PID:331 | env_proxy_lazy.reset:87 - aisrv EnvProxy reset one game success
2025-07-17 22:14:15.362 | INFO | PID:329 | prometheus_utils.push_to_prometheus_gateway:294 - aisrv push_to_gateway failed, error is <urlopen error [Errno -2] Name or service not known>, prometheus_pushgateway is prometheus-pushgateway:9091
