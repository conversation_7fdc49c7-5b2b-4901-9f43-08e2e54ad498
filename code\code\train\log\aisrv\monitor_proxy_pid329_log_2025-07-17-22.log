{"time": "2025-07-17 22:10:44.624151", "level": "INFO", "message": "aisrv monitor_proxy process start success at pid is 329", "file": "monitor_proxy_process.py", "line": "73", "module": "aisrv", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 329}
{"time": "2025-07-17 22:14:15.362745", "level": "INFO", "message": "aisrv push_to_gateway failed, error is <urlopen error [Errno -2] Name or service not known>, prometheus_pushgateway is prometheus-pushgateway:9091", "file": "prometheus_utils.py", "line": "294", "module": "aisrv", "process": "prometheus_utils", "function": "push_to_prometheus_gateway", "stack": "", "pid": 329}
