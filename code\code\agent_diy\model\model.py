#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import torch
import numpy as np
from torch import nn
import torch.nn.functional as F
from kaiwu_agent.utils.common_func import attached
from torch.distributions.categorical import Categorical
from torch import nn


class Model(nn.Module):
    def __init__(self, state_shape, action_shape):
        super().__init__()
        self.feat_sz1 = 128
        self.feat_sz2 = 128

        self.simple_net = nn.Sequential(
                nn.Linear(state_shape, 256),
                nn.<PERSON>L<PERSON>(),
                nn.Linear(256, self.feat_sz1),
                nn.<PERSON>L<PERSON>(),
            )
        
        self.actor = nn.Linear(self.feat_sz1, action_shape)
        self.critic = nn.Linear(self.feat_sz1, 1)


    def forward(self, x):
        x1 = self.simple_net(x)
        logits = self.actor(x1)
        value = self.critic(x1)
        return logits, value

    # 数据收集和策略更新两个不同的阶段
    def sample(self, x, action=None):
        logits, value = self.forward(x)
        probs = Categorical(logits=logits)
        if action is  None:
            action = probs.sample()
        logprob = probs.log_prob(action)
        return action, logprob, probs.entropy(), value
    
    def get_maxprob_action(self, x):
        logits, value = self.forward(x)
        return nn.functional.softmax(logits, dim=-1).argmax(dim=-1)

        

