{"time": "2025-07-16 10:18:25.784221", "level": "INFO", "message": "env monitor_proxy process start success at pid is 95", "file": "monitor_proxy_process.py", "line": "84", "module": "env", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 95}
{"time": "2025-07-16 10:47:48.822738", "level": "INFO", "message": "env push_to_gateway failed, error is <urlopen error [Errno -5] No address associated with hostname>, prometheus_pushgateway is prometheus-pushgateway:9091", "file": "prometheus_utils.py", "line": "327", "module": "env", "process": "prometheus_utils", "function": "push_to_prometheus_gateway", "stack": "", "pid": 95}
