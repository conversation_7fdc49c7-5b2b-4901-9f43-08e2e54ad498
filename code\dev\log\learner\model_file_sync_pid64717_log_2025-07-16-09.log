{"time": "2025-07-16 09:24:10.239030", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 64717}
{"time": "2025-07-16 09:24:10.369482", "level": "INFO", "message": "model_file_sync process pid is 64717", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 64717}
{"time": "2025-07-16 09:24:10.451812", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_q_learning/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 64717}
