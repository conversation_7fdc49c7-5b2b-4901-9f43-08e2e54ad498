#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import numpy as np
from kaiwu_agent.utils.common_func import create_cls, attached

# bison： trick，把monitor_data指针直接放到样本数据里，方便learn函数在里面保存loss值
SampleData = create_cls("SampleData", state=None, action=None, reward=None, next_state=None, done=None, img=None, next_img=None, monitor=None)


@attached
def sample_process(list_game_data):
    return [SampleData(**i.__dict__) for i in list_game_data]


def get_location_memory(extra_info):
    game_info = extra_info["game_info"]
    hero_pos = [game_info["pos_x"], game_info["pos_z"]]
    mem = game_info["location_memory"][hero_pos[0]*64+hero_pos[1]]
    assert mem <= 1.0 and mem >= 0.0, ""
    return mem


def reward_shaping(frame_no, score, terminated, truncated, obs, _obs):
    _extra_info = _obs['extra_info'] #新状态下的额外信息
    mem = get_location_memory(_extra_info) #英雄所在位置的记忆

    reward = 0
    end_treasure_dists = obs["feature"]
    _end_treasure_dists = _obs["feature"]

    #奖励 0， 惩罚在一个地方反复出现，从而鼓励探索
    reward -= mem * 0.5

    # The reward for winning
    # 奖励1. 获胜的奖励
    if terminated:
        reward += 2

    # # The reward for being close to the finish line
    # # 奖励2. 靠近终点的奖励:
    end_dist, _end_dist = end_treasure_dists[0], _end_treasure_dists[0]
    if end_dist > _end_dist:
        reward += 0.1

    # The reward for obtaining a treasure chest
    # 奖励3. 获得宝箱的奖励
    if score > 0 and not terminated:
        reward += 1

    # # The reward for being close to the treasure chest (considering only the nearest one)
    # # 奖励4. 靠近宝箱的奖励(只考虑最近的那个宝箱)
    treasure_dist, _treasure_dist = end_treasure_dists[1:], _end_treasure_dists[1:]
    nearest_treasure_index = np.argmin(treasure_dist)
    if treasure_dist[nearest_treasure_index] > _treasure_dist[nearest_treasure_index]:
        reward += 0.1

    return reward
