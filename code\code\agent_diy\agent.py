#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import kaiwu_agent
from kaiwu_agent.agent.base_agent import BaseAgent
from kaiwu_agent.utils.common_func import create_cls
import numpy as np
import os

from agent_diy.algorithm.algorithm import Algorithm
from kaiwu_agent.agent.base_agent import (
    learn_wrapper,
    save_model_wrapper,
    load_model_wrapper,
    predict_wrapper,
    exploit_wrapper,
    check_hasattr,
)
from agent_diy.conf.conf import Config
import torch
from kaiwu_agent.utils.common_func import attached

ObsData = create_cls("ObsData", feature=None)
ActData = create_cls("ActData", act=None, value=None, logp=None)

@attached
class Agent(BaseAgent):
    def __init__(self, agent_type="player", device=None, logger=None, monitor=None) -> None:
        self.logger = logger

        # Initialize parameters
        # 参数初始化
        self.state_size = Config.STATE_DIM
        self.action_size = Config.ACTION_DIM
        self.learning_rate = Config.LEARNING_RATE
        self.gamma = Config.GAMMA
        self.algorithm = Algorithm()

        super().__init__(agent_type, device, logger, monitor)

        # Load pre-trained model if specified
        # 如果指定了预训练模型ID，则加载预训练模型
        if Config.LOAD_MODEL_ID:
            if self.logger:
                self.logger.info("Load pre-trained model.")
            self.__load_model(
                path="/data/ckpt/gorge_walk_v2_diy",
                id=str(Config.LOAD_MODEL_ID),
            )

    @predict_wrapper
    def predict(self, list_obs_data):
        """
        The input is list_obs_data, and the output is list_act_data.
        """
        """
        输入是 list_obs_data, 输出是 list_act_data
        """
        state= list_obs_data[0].feature
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(Config.DEVICE)
        with torch.no_grad():
            action, logprob, _, value = self.algorithm.net.sample(state_tensor)

        return [ActData(act=action.cpu().item(), logp=logprob.cpu().item(), value=value[0].cpu().item())]

    @exploit_wrapper
    def exploit(self, observation):
        obs_data = self.observation_process(observation["obs"], observation["extra_info"])
        state = obs_data.feature
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(Config.DEVICE)
        
        with torch.no_grad():
            action, logprob, _, value = self.algorithm.net.sample(state_tensor)
        act = action.cpu().item()
        
        return act

    

    @learn_wrapper
    def learn(self, list_sample_data):
        return self.algorithm.learn(list_sample_data)

    def observation_process(self, raw_obs, extra_info):

        game_info = extra_info["game_info"]
        pos = [game_info["pos_x"], game_info["pos_z"]]
        # Feature #1: Current state of the agent (1-dimensional representation)
        # 特征#1: 智能体当前 state (1维表示)
        state = [int(pos[0] * 64 + pos[1])]
        # Feature #2: One-hot encoding of the agent's current position
        # 特征#2: 智能体当前位置信息的 one-hot 编码
        pos_row = [0] * 64
        pos_row[pos[0]] = 1
        pos_col = [0] * 64
        pos_col[pos[1]] = 1

        # Feature #3: Discretized distance of the agent's current position from the endpoint
        # 特征#3: 智能体当前位置相对于终点的距离(离散化)
        # Feature #4: Discretized distance of the agent's current position from the treasure
        # 特征#4: 智能体当前位置相对于宝箱的距离(离散化)
        #end_treasure_dists = raw_obs["feature"] # 这个字段是11个距离，依次为到终点和宝箱的距离，如果宝箱不存在，999
        # bison:999这个值太大了，估计对稳定收敛有影响，改为0，这样做对于一开始没有宝箱的环境应该是ok的
        #end_treasure_dists = [0 if x == 999 else x for x in end_treasure_dists]

        # Feature #5: Graph features generation (obstacle information, treasure information, endpoint information)
        # 特征#5: 图特征生成(障碍物信息, 宝箱信息, 终点信息)
        local_view = [game_info["local_view"][i : i + 5] for i in range(0, len(game_info["local_view"]), 5)]
        obstacle_map, treasure_map, end_map = [], [], []
        for sub_list in local_view:
            obstacle_map.append([1 if i == 0 else 0 for i in sub_list])
            treasure_map.append([1 if i == 4 else 0 for i in sub_list])
            end_map.append([1 if i == 3 else 0 for i in sub_list])

        # Feature #6: Conversion of graph features into vector features
        # 特征#6: 图特征转换为向量特征
        obstacle_flat, treasure_flat, end_flat = [], [], []
        for i in obstacle_map:
            obstacle_flat.extend(i)
        for i in treasure_map:
            treasure_flat.extend(i)
        for i in end_map:
            end_flat.extend(i)

        # Feature #7: Information of the map areas visited within the agent's current local view
        # 特征#7: 智能体当前局部视野中的走过的地图信息
        # bison:这里可以推断拉平的局部数据和全局坐标的关系，x坐标是粗粒度的变化，delta_x = [-2,2], 在x固定的时候，y坐标做细粒度的变化，delta_y=[-2,2]
        memory_flat = []
        for i in range(game_info["view"] * 2 + 1):
            idx_start = (pos[0] - game_info["view"] + i) * 64 + (pos[1] - game_info["view"])
            memory_flat.extend(game_info["location_memory"][idx_start : (idx_start + game_info["view"] * 2 + 1)])

        tmp_treasure_status = [x if x != 2 else 0 for x in game_info["treasure_status"]]

        # Feature#8 ,允许的动作也编码进去
        # raw_obs['legal_act'] 我看了一下，基本上都是0，1，2，3,可能环境没有真正去做每个状态下的合法动作的细致化
        assert len(raw_obs['legal_act'] ) == 4, "legal act"
        

        feature = np.concatenate(
            [
                #state, #这个值可能会很大, 而且和后面两个字段比较重复，干掉...
                pos_row,
                pos_col,
                #end_treasure_dists,
                obstacle_flat,
                treasure_flat,
                end_flat,
                memory_flat,
                tmp_treasure_status,
            ]
        ) # 长度238的np数组

        
        return ObsData(feature=feature) 


    def action_process(self, act_data):
        return act_data.act, act_data.logp, act_data.value

    @save_model_wrapper
    def save_model(self, path=None, id="1"):
        # To save the model, it can consist of multiple files,
        # and it is important to ensure that each filename includes the "model.ckpt-id" field.
        # 保存模型, 可以是多个文件, 需要确保每个文件名里包括了model.ckpt-id字段
        model_file_path = f"{path}/model.ckpt-{str(id)}.pth"

        # 保存完整的模型状态，包括模型参数和优化器状态
        checkpoint = {
            'model_state_dict': self.algorithm.net.state_dict(),
            'optim_state_dict': self.algorithm.optimizer.state_dict(),
            'config': {
                'state_dim': Config.STATE_DIM,
                'action_dim': Config.ACTION_DIM,
                'learning_rate': Config.LEARNING_RATE,
                'gamma': Config.GAMMA,
            }
        }

        torch.save(checkpoint, model_file_path)
        if self.logger:
            self.logger.info(f"save model {model_file_path} successfully")

    @load_model_wrapper
    def load_model(self, path=None, id="1"):
        self.__load_model(path, id)

    def __load_model(self, path=None, id="1"):
        """
        私有方法：加载预训练模型
        Private method: Load pre-trained model
        注意：这个方法不能抛出异常，因为评估模式下可能找不到预训练文件
        Note: This method should not raise exceptions as evaluation mode might not find pre-trained files
        """
        model_file_path = f"{path}/model.ckpt-{str(id)}.pth"
        try:
            # 加载.pth格式的checkpoint
            checkpoint = torch.load(
                model_file_path,
                map_location=Config.DEVICE,
                weights_only=False
            )

            # 加载模型状态
            self.algorithm.net.load_state_dict(checkpoint['model_state_dict'])

            # 加载优化器状态（如果存在）
            if 'optim_state_dict' in checkpoint:
                self.algorithm.optimizer.load_state_dict(checkpoint['optim_state_dict'])

            if self.logger:
                self.logger.info(f"load model {model_file_path} successfully")
        except FileNotFoundError:
            if self.logger:
                self.logger.info(f"File {model_file_path} not found")
            # 不抛出异常，避免评估模式下的问题
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Failed to load model {model_file_path}: {e}")
            # 不抛出异常，继续使用随机初始化的模型

