[dynamic_programming]
actor_model = "agent_dynamic_programming.agent.Agent"
learner_model = "agent_dynamic_programming.agent.Agent"
aisrv_model = "agent_dynamic_programming.agent.Agent"
trainer = "kaiwudrl.server.learner.standard_trainer.StandardTrainer"
predictor = "kaiwudrl.server.actor.standard_predictor.StandardPredictor"
train_workflow = "agent_dynamic_programming.workflow.train_workflow.workflow"
eval_workflow = "agent_dynamic_programming.workflow.eval_workflow.workflow"
exam_workflow = "agent_dynamic_programming.workflow.exam_workflow.workflow"

[monte_carlo]
actor_model = "agent_monte_carlo.agent.Agent"
learner_model = "agent_monte_carlo.agent.Agent"
aisrv_model = "agent_monte_carlo.agent.Agent"
trainer = "kaiwudrl.server.learner.standard_trainer.StandardTrainer"
predictor = "kaiwudrl.server.actor.standard_predictor.StandardPredictor"
train_workflow = "agent_monte_carlo.workflow.train_workflow.workflow"
eval_workflow = "agent_monte_carlo.workflow.eval_workflow.workflow"
exam_workflow = "agent_monte_carlo.workflow.exam_workflow.workflow"

[q_learning]
actor_model = "agent_q_learning.agent.Agent"
learner_model = "agent_q_learning.agent.Agent"
aisrv_model = "agent_q_learning.agent.Agent"
trainer = "kaiwudrl.server.learner.standard_trainer.StandardTrainer"
predictor = "kaiwudrl.server.actor.standard_predictor.StandardPredictor"
train_workflow = "agent_q_learning.workflow.train_workflow.workflow"
eval_workflow = "agent_q_learning.workflow.eval_workflow.workflow"
exam_workflow = "agent_q_learning.workflow.exam_workflow.workflow"

[sarsa]
actor_model = "agent_sarsa.agent.Agent"
learner_model = "agent_sarsa.agent.Agent"
aisrv_model = "agent_sarsa.agent.Agent"
trainer = "kaiwudrl.server.learner.standard_trainer.StandardTrainer"
predictor = "kaiwudrl.server.actor.standard_predictor.StandardPredictor"
train_workflow = "agent_sarsa.workflow.train_workflow.workflow"
eval_workflow = "agent_sarsa.workflow.eval_workflow.workflow"
exam_workflow = "agent_sarsa.workflow.exam_workflow.workflow"

[diy]
actor_model = "agent_diy.agent.Agent"
learner_model = "agent_diy.agent.Agent"
aisrv_model = "agent_diy.agent.Agent"
trainer = "kaiwudrl.server.learner.standard_trainer.StandardTrainer"
predictor = "kaiwudrl.server.actor.standard_predictor.StandardPredictor"
train_workflow = "agent_diy.workflow.train_workflow.workflow"
eval_workflow = "agent_diy.workflow.eval_workflow.workflow"
exam_workflow = "agent_diy.workflow.exam_workflow.workflow"
