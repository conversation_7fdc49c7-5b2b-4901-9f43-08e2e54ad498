#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import numpy as np
from agent_q_learning.model.model import Model
from agent_q_learning.conf.conf import Config
from torch.optim.adam import Adam
import torch
import torch.nn.functional as F
import random




class Algorithm:
    def __init__(self, gamma, learning_rate):
        self.gamma = gamma
        self.learning_rate = learning_rate
        self.device='cuda:0'


        self.state_dim = Config.STATE_DIM
        self.action_dim = Config.ACTION_DIM

        self.qnet = Model(self.state_dim, self.action_dim).to(self.device)
        self.optimizer = Adam(self.qnet.parameters(), lr=learning_rate)
        self.target_qnet = Model(self.state_dim, self.action_dim).to(self.device)
        self.target_qnet.load_state_dict(self.qnet.state_dict())

    def learn(self, list_sample_data):

        batch_size = 128

        if len(list_sample_data) < 4000:
            return None

        
        samples = random.sample(list_sample_data, batch_size)

        monitor_data = samples[0].monitor
     
        samples = [(s.state, s.action, s.reward, s.next_state, s.done, s.img, s.next_img) for s in samples]
        states, actions, rewards, next_states, dones, imgs, next_imgs = zip(*samples)
        
        states = torch.FloatTensor(states).to(self.device)
        imgs = torch.FloatTensor(imgs).to(self.device)
        next_imgs = torch.FloatTensor(next_imgs).to(self.device)
        actions = torch.tensor(actions, dtype=torch.int64).unsqueeze(1).to(self.device)
        rewards = torch.FloatTensor(rewards).unsqueeze(1).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.IntTensor(dones).unsqueeze(1).to(self.device)

        # 计算当前 Q 值
        q_values = self.qnet(states, imgs)
        q_values = q_values.gather(1, actions)  # 从 Q(s, a) 选取执行的动作 Q 值

        # 计算目标 Q 值
        next_q_values = self.target_qnet(next_states, next_imgs).max(1, keepdim=True)[0]  # 选取 Q(s', a') 的最大值
        target_q_values = rewards + Config.GAMMA * next_q_values * (1 - dones)  # TD 目标

        # 计算损失
        loss = F.mse_loss(q_values, target_q_values.detach())
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        monitor_data['diy_3'] = loss.cpu().item()
                
        return loss.cpu().item()

    def update_target_net(self):
        self.target_qnet.load_state_dict(self.qnet.state_dict())
