{"time": "2025-07-17 22:10:44.629955", "level": "INFO", "message": "aisrv monitor_proxy process start success at pid is 330", "file": "monitor_proxy_process.py", "line": "73", "module": "aisrv", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 330}
{"time": "2025-07-17 22:10:44.942816", "level": "INFO", "message": "aisrv policy_name train_one, algo diy, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "aisrv", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 327}
{"time": "2025-07-17 22:10:44.945771", "level": "INFO", "message": "aisrv predict just no model_pool files or copy_model_files failed, please check", "file": "multi_model_common.py", "line": "240", "module": "aisrv", "process": "multi_model_common", "function": "init_load_models", "stack": "", "pid": 327}
{"time": "2025-07-17 22:10:44.946712", "level": "INFO", "message": "predict policy_name: train_one, start success at pid 327, on-policy/off-policy is off-policy, actor_receive_cost_time_ms: 1, predict_batch_size: 1", "file": "actor_proxy_local.py", "line": "663", "module": "aisrv", "process": "actor_proxy_local", "function": "before_run", "stack": "", "pid": 327}
{"time": "2025-07-17 22:10:44.947890", "level": "INFO", "message": "aisrv predict now predict count is 0", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 327}
