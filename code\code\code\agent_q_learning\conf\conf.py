#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


# Configuration of dimensions
# 关于维度的配置
class Config:


    LEARNING_RATE = 3e-4
    GAMMA = 0.99
    EPSILON = 0.1
    EPISODES = 10000
    MAP_SIZE = 64

    STATE_DIM=238
    ACTION_DIM=4

    # dimensionality of the sample
    # 样本维度
    SAMPLE_DIM = 5

    # Dimension of observation
    # 观察维度
    OBSERVATION_SHAPE = 250
