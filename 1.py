#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process
from agent_ppo.conf.conf import Config


def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)


class Preprocessor:
    def __init__(self) -> None:
        self.move_action_num = 8
        self.talent_action_num = 8
        self.total_action_num = 16
        self.reset()

    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        self.last_pos_norm = np.array((0, 0))  # 初始化上一步位置
        self.end_pos = None
        self.is_end_pos_found = False
        self.history_pos = []
        self.bad_move_ids = set()
        self.last_action = -1  # 初始化上一步动作

        # 全局地图信息 (128x128)
        self.global_pos = (64, 64)  # 全局位置，默认地图中心
        self.view_size = 5  # 视野范围，局部地图11x11 (5+1+5)

        # 状态跟踪
        self.collected_treasure_ids = set()
        self.treasure_positions = []
        self.buff_positions = []
        self.obstacle_positions = []
        self.buff_cooldown_remaining = 0  # buff剩余冷却时间
        self.treasure_status = [0] * 13  # 固定13维宝箱状态
        self.treasure_collected_count = 0  # 已收集的宝箱数量

        # 英雄状态
        self.talent_usable = True
        self.talent_cooldown = 0
        self.speed_buff_active = False
        self.speed_buff_remaining = 0

        # 局部地图特征 (11x11)
        self.local_map = np.zeros((11, 11), dtype=np.float32)
        self.obstacle_map = np.zeros((11, 11), dtype=np.float32)
        self.treasure_map = np.zeros((11, 11), dtype=np.float32)
        self.end_map = np.zeros((11, 11), dtype=np.float32)
        self.buff_map = np.zeros((11, 11), dtype=np.float32)
        self.memory_map = np.zeros((11, 11), dtype=np.float32)

        # 全局记忆地图 (用于跟踪全局访问历史)
        self.global_memory = np.zeros((128, 128), dtype=np.float32)

        # 各种flag
        self.end_flag = 0.0
        self.treasure_flag = 0.0
        self.buff_flag = 0.0
        self.obstacle_flag = 0.0
        self.memory_flag = 0.0

    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def _get_pos_onehot(self):
        """生成位置的one-hot编码 128*2维"""
        pos_row = np.zeros(128, dtype=np.float32)
        pos_col = np.zeros(128, dtype=np.float32)

        # 将位置坐标转换为0-127范围内的索引
        row_idx = int(np.clip(self.cur_pos[0], 0, 127))
        col_idx = int(np.clip(self.cur_pos[1], 0, 127))

        pos_row[row_idx] = 1.0
        pos_col[col_idx] = 1.0

        return np.concatenate([pos_row, pos_col])

    def _get_status_info(self):
        """
        生成状态信息：13个箱子+1BUFF+1终点+3维英雄状态 = 18维

        - 13维宝箱状态: 每个宝箱ID对应固定索引 (1=可获取, 0=已收集/不可获取)
        - 1维BUFF状态: 基于冷却时间的归一化状态
        - 1维终点状态: 是否已发现终点
        - 3维英雄状态: [技能可用性, 加速状态, 技能冷却进度]
        """
        # 13个箱子状态 - 直接使用从organs更新的状态
        treasure_status = np.array(self.treasure_status, dtype=np.float32)

        # 1个BUFF状态 (直接从organs获取状态)
        buff_status = np.array([self._get_buff_status_from_organs()], dtype=np.float32)

        # 1个终点状态 (1表示已发现，0表示未发现)
        end_status = np.array([1.0 if self.is_end_pos_found else 0.0], dtype=np.float32)

        # 3维英雄状态
        hero_status = np.array([
            1.0 if self.talent_usable else 0.0,  # 闪现技能可用性
            1.0 if self.speed_buff_active else 0.0,  # 加速buff状态
            1.0 - (float(self.talent_cooldown) / 100.0)  # 技能冷却进度（1.0=可用，0.0=刚用完）
        ], dtype=np.float32)

        return np.concatenate([treasure_status, buff_status, end_status, hero_status])

    def _get_buff_status_from_organs(self):
        """
        直接从organs获取BUFF状态

        返回值:
        - 1.0: 可获取 (status=1)
        - 0.0: 不可获取 (status=0) 或不存在
        """
        # 从当前organs中查找buff
        for organ in getattr(self, 'current_organs', []):
            if organ["sub_type"] == 2:  # buff类型
                status = organ.get("status", 0)
                return 1.0 if status == 1 else 0.0

        # 如果没有找到buff，返回0.0
        return 0.0



    def _get_all_pos_status(self):
        """
        生成6*15=90维全部目标位置特征
        15个目标：13个宝箱(config_id 1-13) + 1个buff(config_id 0) + 1个终点(config_id 22)
        每个目标6维特征：[manhattan_distance, relative_direction, status, x_normalized, z_normalized, l2_distance]
        - manhattan_distance: 曼哈顿距离
        - relative_direction: 相对方向(0-8: None/E/NE/N/NW/W/SW/S/SE)
        - status: 状态(0=不可获取, 1=可获取)
        - x_normalized: x坐标归一化(0-1)
        - z_normalized: z坐标归一化(0-1)
        - l2_distance: 相对距离等级(0-5: None/0~20/20~40/40~60/60~80/80~180网格)
        """
        features = np.zeros((15, 6), dtype=np.float32)

        # 初始化所有槽位为空（全零特征）
        # 槽位分配：0-12=宝箱, 13=buff, 14=终点

        # 处理地图中所有目标organs（包含全局信息）
        for organ in getattr(self, 'current_organs', []):
            target_idx = self._get_target_index(organ)
            if target_idx is not None:
                features[target_idx] = self._extract_target_features(organ, exists=True)

        return features.flatten()  # 返回90维特征 (15目标 × 6维)

    def _get_target_index(self, organ):
        """获取目标在15个位置中的索引"""
        if organ["sub_type"] == 1:  # 宝箱
            config_id = organ["config_id"]
            if 1 <= config_id <= 13:
                return config_id - 1  # 索引0-12 (宝箱槽位，ID1->索引0, ID2->索引1, ...)
        elif organ["sub_type"] == 2:  # buff
            return 13  # 索引13 (buff槽位)
        elif organ["sub_type"] == 4:  # 终点
            return 14  # 索引14 (终点槽位)
        # 忽略起点 (sub_type == 3)
        return None

    def _extract_target_features(self, organ, exists=True):
        """提取单个目标的6维特征"""
        features = np.zeros(6, dtype=np.float32)

        if not exists or organ is None:
            # 空槽位：所有特征为0，通过全零自然区分不存在的目标
            return features

        # 存在的目标：正常特征提取
        # 1维：曼哈顿距离
        features[0] = self._get_manhattan_distance(organ)

        # 2维：相对方向特征
        features[1] = self._get_relative_direction(organ)

        # 3维：状态 (0=不可获取, 1=可获取, -1=视野外)
        features[2] = self._get_status_feature(organ)

        # 4维：x坐标归一化
        features[3] = self._get_x_normalized(organ)

        # 5维：z坐标归一化
        features[4] = self._get_z_normalized(organ)

        # 6维：L2距离 (从organs中的relative_pos获取)
        features[5] = self._get_l2_distance_feature(organ)

        return features

    def _get_manhattan_distance(self, organ):
        """曼哈顿距离特征"""
        # 对于终点，如果pos为(-1,-1)，使用预测位置
        if organ["sub_type"] == 4 and organ["pos"]["x"] < 0:
            if hasattr(self, 'end_pos') and self.end_pos:
                organ_pos = {"x": self.end_pos[0], "z": self.end_pos[1]}
            else:
                # 如果没有预测位置，返回最大距离
                return 1.0
        else:
            organ_pos = organ["pos"]

        hero_pos = self.cur_pos
        manhattan_dist = abs(organ_pos["x"] - hero_pos[0]) + abs(organ_pos["z"] - hero_pos[1])
        # 归一化曼哈顿距离（最大距离约为256）
        return manhattan_dist / 256.0

    def _get_relative_direction(self, organ):
        """相对方向特征：使用relative_pos中的direction"""
        rel_pos = organ.get("relative_pos", {})
        if "direction" in rel_pos:
            direction_key = rel_pos["direction"]
            if isinstance(direction_key, str):
                return RelativeDirection.get(direction_key, 0) / 8.0
            else:
                return float(direction_key) / 8.0
        return 0.0

    def _get_status_feature(self, organ):
        """状态特征：直接使用status值"""
        return float(organ.get("status", 0))

    def _get_x_normalized(self, organ):
        """x坐标归一化：目标的x坐标归一化到[0,1]"""
        # 对于终点，如果pos为(-1,-1)，使用预测位置
        if organ["sub_type"] == 4 and organ["pos"]["x"] < 0:
            if hasattr(self, 'end_pos') and self.end_pos:
                return self.end_pos[0] / 128.0
            else:
                return 0.0  # 如果没有预测位置，返回0

        # 其他目标使用原始位置
        return organ["pos"]["x"] / 128.0

    def _get_z_normalized(self, organ):
        """z坐标归一化：目标的z坐标归一化到[0,1]"""
        # 对于终点，如果pos为(-1,-1)，使用预测位置
        if organ["sub_type"] == 4 and organ["pos"]["z"] < 0:
            if hasattr(self, 'end_pos') and self.end_pos:
                return self.end_pos[1] / 128.0
            else:
                return 0.0  # 如果没有预测位置，返回0

        # 其他目标使用原始位置
        return organ["pos"]["z"] / 128.0

    def _get_l2_distance_feature(self, organ):
        """L2距离特征，从organs的relative_pos中获取相对距离等级"""
        relative_pos = organ.get("relative_pos", {})
        l2_distance_key = relative_pos.get("l2_distance", "VeryLarge")

        if isinstance(l2_distance_key, str):
            # 字符串形式的距离，如'VerySmall', 'Small'等
            l2_distance_value = RelativeDistance.get(l2_distance_key, 5)
        else:
            # 数字形式的距离
            l2_distance_value = l2_distance_key

        # 归一化L2距离等级，范围0-5
        return float(l2_distance_value) / 5.0



    def _create_map_feature(self):
        """
        创建多通道地图特征，每个通道表示不同类型的对象

        返回形状为 (605,) 的flatten地图特征：
        - 通道0：障碍物地图 (obstacle_map) - 121维
        - 通道1：宝箱地图 (treasure_map) - 121维
        - 通道2：终点地图 (end_map) - 121维
        - 通道3：buff地图 (buff_map) - 121维
        - 通道4：记忆地图 (memory_map) - 121维
        总计：5 × 11 × 11 = 605维
        """
        # 将5个分离的地图堆叠成多通道特征
        map_feature = np.stack([
            self.obstacle_map,  # 通道0：障碍物
            self.treasure_map,  # 通道1：宝箱
            self.end_map,       # 通道2：终点
            self.buff_map,      # 通道3：buff
            self.memory_map     # 通道4：记忆
        ], axis=0)

        # 展平为一维数组
        return map_feature.flatten()

    def _process_local_map(self, map_info):
        """处理11x11局部地图信息"""
        # 重置除记忆地图外的所有地图
        self.local_map.fill(0)
        self.treasure_map.fill(0)
        self.end_map.fill(0)
        self.obstacle_map.fill(0)
        self.buff_map.fill(0)

        if isinstance(map_info, list) and len(map_info) > 0:
            for y in range(min(11, len(map_info))):
                row_data = map_info[y]
                if isinstance(row_data, dict) and "values" in row_data:
                    values = row_data["values"]
                    for x in range(min(11, len(values))):
                        cell_value = float(values[x])
                        self.local_map[y, x] = cell_value

                        # 根据地图值填充对应的专用地图
                        if cell_value == 0:  # 不可通行（障碍物）
                            self.obstacle_map[y, x] = 1.0
                        elif cell_value == 3:  # 终点
                            self.end_map[y, x] = 1.0
                        elif cell_value == 4:  # 宝箱
                            self.treasure_map[y, x] = 1.0
                        elif cell_value == 6:  # 加速增益
                            self.buff_map[y, x] = 1.0

    def _update_memory_map(self):
        """更新记忆地图"""
        # 全局记忆衰减
        self.memory_map *= 0.99
        self.global_memory *= 0.995

        # 英雄在局部地图中心(5,5)
        hero_local_x, hero_local_y = 5, 5
        self.memory_map[hero_local_y, hero_local_x] = min(
            self.memory_map[hero_local_y, hero_local_x] + 0.1, 1.0
        )

        # 更新全局记忆
        global_x, global_y = self.global_pos
        if 0 <= global_x < 128 and 0 <= global_y < 128:
            self.global_memory[global_y, global_x] = min(
                self.global_memory[global_y, global_x] + 0.05, 1.0
            )

    def _process_organs(self, organs):
        """
        处理organs信息，包括宝箱ID与索引的映射
        注意：organs现在包含地图中所有目标的全局信息，不仅仅是视野内可见的
        """
        # 保存当前organs信息供_get_all_pos_status使用
        self.current_organs = organs

        # 重置宝箱状态为全0（13维固定）
        self.treasure_status = [0] * 13

        # 注意：以下位置列表主要用于全局状态跟踪，视野内状态由map_info提供
        self.treasure_positions = []
        self.buff_positions = []
        self.obstacle_positions = []

        for organ in organs:
            if organ["sub_type"] == 1:  # 宝箱
                config_id = organ["config_id"]
                if 1 <= config_id <= 13:  # 宝箱ID范围1-13
                    index = config_id - 1  # 转换为0-12索引

                    if organ["status"] == 0:  # 不可获取(已收集)
                        self.collected_treasure_ids.add(config_id)
                        self.treasure_status[index] = 0  # 已收集，状态为0
                    elif organ["status"] == 1:  # 可获取
                        self.treasure_positions.append((organ["pos"]["x"], organ["pos"]["z"]))
                        self.treasure_status[index] = 1  # 可获取，状态为1


            elif organ["sub_type"] == 2:  # 加速buff
                # buff状态现在直接从organs获取，不需要额外处理
                # 保留位置信息用于其他可能的用途
                if organ["status"] == 1:  # 可获取（冷却结束）
                    self.buff_positions.append((organ["pos"]["x"], organ["pos"]["z"]))

            elif organ["sub_type"] == 4:  # 终点
                if organ["status"] == 1:
                    self.end_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    self.is_end_pos_found = True
                else:
                    # 使用相对位置预测终点
                    self._predict_end_pos_from_relative(organ)



    def _predict_end_pos_from_relative(self, organ):
        """根据相对位置预测终点位置"""
        rel_pos = organ["relative_pos"]
        end_pos_dis_key = rel_pos["l2_distance"]
        end_pos_dir_key = rel_pos["direction"]

        # 处理距离和方向
        if isinstance(end_pos_dis_key, str):
            end_pos_dis = RelativeDistance.get(end_pos_dis_key, 0)
        else:
            end_pos_dis = end_pos_dis_key

        if isinstance(end_pos_dir_key, str):
            end_pos_dir = RelativeDirection.get(end_pos_dir_key, 0)
        else:
            end_pos_dir = end_pos_dir_key

        # 计算预测位置
        distance = end_pos_dis * 20
        theta = DirectionAngles.get(end_pos_dir, 0)
        delta_x = distance * math.cos(math.radians(theta))
        delta_z = distance * math.sin(math.radians(theta))

        self.end_pos = (
            max(0, min(128, round(self.cur_pos[0] + delta_x))),
            max(0, min(128, round(self.cur_pos[1] + delta_z))),
        )

    def _update_hero_status(self, obs):
        """更新英雄状态"""
        # 从环境获取技能状态
        env_legal_action = obs.get("legal_act", [1, 1])
        self.talent_usable = bool(env_legal_action[1]) if len(env_legal_action) > 1 else True

        # 从heroes信息获取加速buff状态
        heroes = obs.get("frame_state", {}).get("heroes", [])
        if heroes and len(heroes) > 0:
            hero = heroes[0]  # 获取第一个英雄

            # 更新加速buff状态
            speed_up = hero.get("speed_up", 0)
            self.speed_buff_active = bool(speed_up)  # 0:否, 1:是
            self.speed_buff_remaining = hero.get("buff_remain_time", 0)

            # 更新技能冷却状态
            talent_info = hero.get("talent", {})
            if talent_info:
                self.talent_cooldown = talent_info.get("cooldown", 0)
                # talent_usable 已经从 legal_act 获取，这里可以作为验证
                talent_status = talent_info.get("status", 1)
                # 如果legal_act和talent状态不一致，以legal_act为准

        # 更新历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

    def _update_flags(self):
        """更新各种flag - 完全基于11x11地图特征"""
        self.end_flag = 1.0 if np.sum(self.end_map) > 0 else 0.0           # 视野内是否有终点
        self.treasure_flag = 1.0 if np.sum(self.treasure_map) > 0 else 0.0  # 视野内是否有宝箱
        self.buff_flag = 1.0 if np.sum(self.buff_map) > 0 else 0.0          # 视野内是否有buff
        self.obstacle_flag = 1.0 if np.sum(self.obstacle_map) > 0 else 0.0  # 视野内是否有障碍物
        self.memory_flag = 1.0 if np.sum(self.memory_map) > 0 else 0.0      # 是否有记忆信息

    def pb2struct(self, frame_state, last_action):
        """根据图片流程实现observation_process"""
        obs, _ = frame_state

        # 获取游戏信息
        self.step_no = obs["frame_state"]["step_no"]

        # Feature #1: Current state of the agent (1-dimensional representation)
        # 特征1: 当前智能体状态(一维表示)
        hero = obs["frame_state"]["heroes"][0]
        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])
        self.global_pos = self.cur_pos  # 更新全局位置

        # 位置坐标归一化 [pos[0] / 64.0, pos[1] / 64.0]
        self.cur_pos_norm = np.array([self.cur_pos[0] / 64.0, self.cur_pos[1] / 64.0], dtype=np.float32)

        # Feature #2: One-hot encoding of the agent's current position
        # 特征2: 智能体当前位置的one-hot编码
        # 这里使用128x128的全局坐标系统

        # Feature #3: 宝箱距离特征 (离散化距离)
        # 处理宝箱状态
        self.treasure_status = [0] * 13  # 重置为固定13维宝箱状态

        # Feature #4: Graph features generation (obstacle, treasure, endpoint information)
        # 特征4: 图特征生成(障碍物信息、宝箱信息、终点信息)

        # 处理局部地图信息 (11x11)
        if "map_info" in obs:
            self._process_local_map(obs["map_info"])

        # Feature #5: Information of the map areas visited within the agent's current local view
        # 特征5: 智能体当前局部视野中已访问地图区域的信息
        self._update_memory_map()

        # 处理organs信息
        self._process_organs(obs["frame_state"]["organs"])

        # 从score_info更新宝箱收集数量
        self._update_treasure_count_from_score(obs)

        # 更新英雄状态
        self._update_hero_status(obs)

        # 更新各种flag
        self._update_flags()

        # 保存上一步动作
        self.last_action = last_action

    def _update_treasure_count_from_score(self, obs):
        """从score_info更新宝箱收集数量"""
        if "score_info" in obs:
            score_info = obs["score_info"]
            self.treasure_collected_count = score_info.get("treasure_collected_count", 0)

        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)
        self.feature_end_pos = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)

        # History position feature
        # 历史位置特征
        if len(self.history_pos) > 0:
            self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])
        else:
            self.feature_history_pos = np.zeros(6, dtype=np.float32)

        self.move_usable = True

    def process(self, frame_state, last_action: int, terminated: bool = False):
        self.pb2struct(frame_state, last_action)

        # Legal action - 16维合法动作
        legal_action = self.get_legal_action()

        # 将所有特征合并为一个完整的特征向量
        feature = np.concatenate([
            # 2维 归一化的位置信息
            self.cur_pos_norm,
            # 256维 位置one-hot (128*2)
            self._get_pos_onehot(),
            # 18维 状态信息 (13个箱子+1BUFF+1终点+3维英雄状态)
            self._get_status_info(),
            # 6维 归一化的历史位置特征
            self.feature_history_pos,
            # 16维 移动方向的合法动作
            np.array(legal_action, dtype=np.float32),
            # 90维 全部目标位置特征 (6*15)
            self._get_all_pos_status(),
            # 605维 flatten地图特征 (5通道×11×11)
            self._create_map_feature(),
        ])

        return (
            feature,
            legal_action,
            reward_process(self.feature_end_pos[-1], self.feature_history_pos[-1]),
        )

    def get_legal_action(self):
        # if last_action is move and current position is the same as last position, add this action to bad_move_ids
        # 如果上一步的动作是移动，且当前位置与上一步位置相同，则将该动作加入到bad_move_ids中
        if (
            abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 0.001
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 0.001
            and self.last_action > -1
            and self.last_action < 8  # 只对移动动作进行碰撞检测
        ):
            self.bad_move_ids.add(self.last_action)
        else:
            # 只清除移动相关的bad_move_ids
            if self.last_action >= 8:  # 如果上次是闪现动作，不清除bad_move_ids
                pass
            else:
                self.bad_move_ids = set()

        # 生成16维legal_action：前8维是移动，后8维是超级闪现
        legal_action = []

        # 前8维：移动动作 (0-7)
        move_legal = [1 if self.move_usable else 0] * self.move_action_num
        for move_id in self.bad_move_ids:
            if move_id < 8:  # 确保只影响移动动作
                move_legal[move_id] = 0
        legal_action.extend(move_legal)

        # 后8维：超级闪现动作 (8-15)
        talent_legal = [1 if self.talent_usable else 0] * self.talent_action_num
        legal_action.extend(talent_legal)

        # 安全检查：确保至少有一个动作可用
        if sum(legal_action) == 0:
            self.bad_move_ids = set()
            legal_action = [1] * self.total_action_num

        return legal_action