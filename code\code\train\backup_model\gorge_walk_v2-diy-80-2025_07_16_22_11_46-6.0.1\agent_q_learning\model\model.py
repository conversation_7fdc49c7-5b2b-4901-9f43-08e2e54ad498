#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import torch
import numpy as np
from torch import nn
from kaiwu_agent.utils.common_func import attached


class Model(nn.Module):
    """
    Deep Q-Network (DQN) model for Q-learning algorithm
    用于Q-learning算法的深度Q网络模型
    """
    def __init__(self, state_shape, action_shape):
        super().__init__()

        self.state_shape = state_shape
        self.action_shape = action_shape

        # Calculate input dimension
        # 计算输入维度
        if isinstance(state_shape, (list, tuple)):
            self.input_dim = int(np.prod(state_shape))
        else:
            self.input_dim = int(state_shape)

        # Deep Q-Network architecture
        # 深度Q网络架构
        self.feature_extractor = nn.Sequential(
            nn.Linear(self.input_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
        )

        # Q-value head
        # Q值输出头
        self.q_head = nn.Linear(128, action_shape)

        # Initialize weights
        # 初始化权重
        self._initialize_weights()

    def _initialize_weights(self):
        """
        Initialize network weights using Xavier initialization
        使用Xavier初始化网络权重
        """
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0.0)

    def forward(self, state):
        """
        Forward pass through the network
        网络前向传播

        Args:
            state: Input state tensor [batch_size, state_dim]

        Returns:
            Q-values for all actions [batch_size, action_dim]
        """
        # Ensure input is properly shaped
        # 确保输入形状正确
        if len(state.shape) == 1:
            state = state.unsqueeze(0)

        # Feature extraction
        # 特征提取
        features = self.feature_extractor(state)

        # Q-value prediction
        # Q值预测
        q_values = self.q_head(features)

        return q_values

    def get_q_values(self, state):
        """
        Get Q-values for a given state
        获取给定状态的Q值

        Args:
            state: Input state (numpy array or tensor)

        Returns:
            Q-values as numpy array
        """
        if isinstance(state, np.ndarray):
            state = torch.FloatTensor(state)

        with torch.no_grad():
            q_values = self.forward(state)

        return q_values.cpu().numpy()

    def get_action(self, state, epsilon=0.0):
        """
        Get action using epsilon-greedy policy
        使用ε-贪心策略获取动作

        Args:
            state: Input state
            epsilon: Exploration probability

        Returns:
            Selected action index
        """
        if np.random.random() < epsilon:
            # Random action (exploration)
            # 随机动作（探索）
            return np.random.randint(self.action_shape)
        else:
            # Greedy action (exploitation)
            # 贪心动作（利用）
            q_values = self.get_q_values(state)
            return np.argmax(q_values)

    def update_target_network(self, source_network, tau=1.0):
        """
        Update target network weights
        更新目标网络权重

        Args:
            source_network: Source network to copy from
            tau: Soft update parameter (1.0 for hard update)
        """
        for target_param, source_param in zip(self.parameters(), source_network.parameters()):
            target_param.data.copy_(tau * source_param.data + (1.0 - tau) * target_param.data)


@attached
class DQNTrainer:
    """
    Deep Q-Network trainer with experience replay and target network
    带有经验回放和目标网络的深度Q网络训练器
    """
    def __init__(self, state_shape, action_shape, learning_rate=0.001, gamma=0.95,
                 buffer_size=10000, batch_size=32, target_update_freq=100):
        self.state_shape = state_shape
        self.action_shape = action_shape
        self.gamma = gamma
        self.batch_size = batch_size
        self.target_update_freq = target_update_freq
        self.update_count = 0

        # Initialize networks
        # 初始化网络
        self.q_network = Model(state_shape, action_shape)
        self.target_network = Model(state_shape, action_shape)

        # Copy weights to target network
        # 复制权重到目标网络
        self.target_network.load_state_dict(self.q_network.state_dict())

        # Optimizer and loss function
        # 优化器和损失函数
        self.optimizer = torch.optim.Adam(self.q_network.parameters(), lr=learning_rate)
        self.criterion = nn.MSELoss()

        # Experience replay buffer
        # 经验回放缓冲区
        self.replay_buffer = []
        self.buffer_size = buffer_size

    def add_experience(self, state, action, reward, next_state, done):
        """
        Add experience to replay buffer
        添加经验到回放缓冲区
        """
        experience = (state, action, reward, next_state, done)

        if len(self.replay_buffer) >= self.buffer_size:
            self.replay_buffer.pop(0)

        self.replay_buffer.append(experience)

    def sample_batch(self):
        """
        Sample a batch of experiences from replay buffer
        从回放缓冲区采样一批经验
        """
        if len(self.replay_buffer) < self.batch_size:
            return None

        indices = np.random.choice(len(self.replay_buffer), self.batch_size, replace=False)
        batch = [self.replay_buffer[i] for i in indices]

        states = torch.FloatTensor([e[0] for e in batch])
        actions = torch.LongTensor([e[1] for e in batch])
        rewards = torch.FloatTensor([e[2] for e in batch])
        next_states = torch.FloatTensor([e[3] for e in batch])
        dones = torch.BoolTensor([e[4] for e in batch])

        return states, actions, rewards, next_states, dones

    def train_step(self):
        """
        Perform one training step
        执行一次训练步骤
        """
        batch = self.sample_batch()
        if batch is None:
            return None

        states, actions, rewards, next_states, dones = batch

        # Current Q-values
        # 当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))

        # Target Q-values
        # 目标Q值
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)

        # Compute loss
        # 计算损失
        loss = self.criterion(current_q_values.squeeze(), target_q_values)

        # Optimize
        # 优化
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        # Update target network
        # 更新目标网络
        self.update_count += 1
        if self.update_count % self.target_update_freq == 0:
            self.target_network.load_state_dict(self.q_network.state_dict())

        return loss.item()

    def get_action(self, state, epsilon=0.0):
        """
        Get action using epsilon-greedy policy
        使用ε-贪心策略获取动作
        """
        return self.q_network.get_action(state, epsilon)

    def save_model(self, path):
        """
        Save model weights
        保存模型权重
        """
        torch.save({
            'q_network': self.q_network.state_dict(),
            'target_network': self.target_network.state_dict(),
            'optimizer': self.optimizer.state_dict(),
        }, path)

    def load_model(self, path):
        """
        Load model weights
        加载模型权重
        """
        checkpoint = torch.load(path)
        self.q_network.load_state_dict(checkpoint['q_network'])
        self.target_network.load_state_dict(checkpoint['target_network'])
        self.optimizer.load_state_dict(checkpoint['optimizer'])
