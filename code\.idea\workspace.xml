<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2537ac1b-9451-493b-81b0-d35cfd48e12f" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/code/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/code/agent_diy/feature/definition.py" beforeDir="false" afterPath="$PROJECT_DIR$/code/agent_diy/feature/definition.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/code/agent_diy/model/model.py" beforeDir="false" afterPath="$PROJECT_DIR$/code/agent_diy/model/model.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/code/train_test.py" beforeDir="false" afterPath="$PROJECT_DIR$/code/train_test.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="30EpClmmHxskeIMpWcRw3sFfu1J" />
  <component name="ProjectViewState">
    <option name="foldersAlwaysOnTop" value="false" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="sortKey" value="BY_TIME_ASCENDING" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/OneDrive/桌面/kaiwu/code&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.18034.82" />
        <option value="bundled-python-sdk-975db3bf15a3-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2537ac1b-9451-493b-81b0-d35cfd48e12f" name="Changes" comment="" />
      <created>1753202461195</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753202461195</updated>
      <workItem from="1753202462433" duration="2421000" />
      <workItem from="1753629274585" duration="266000" />
      <workItem from="1753631910040" duration="3514000" />
      <workItem from="1753766440568" duration="58000" />
      <workItem from="1754144345160" duration="1512000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>