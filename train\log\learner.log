2025-07-17 22:10:34.763063: I tensorflow/core/util/port.cc:110] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-07-17 22:10:36.094486: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-07-17 22:10:37.526843: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT
2025-07-17 22:10:38.769 | INFO | PID:216 | trainer.__init__:32 - learner train process start at pid is 216
2025-07-17 22:10:38.836 | INFO | PID:216 | replay_buffer_wrapper.__init__:58 - learner train replaybuff, use reverb
[reverb/cc/platform/tfrecord_checkpointer.cc:162]  Initializing TFRecordCheckpointer in /tmp/tmpt0vgsf7l.
[reverb/cc/platform/tfrecord_checkpointer.cc:567] Loading latest checkpoint from /tmp/tmpt0vgsf7l
[reverb/cc/platform/default/server.cc:71] Started replay server on port 9999
2025-07-17 22:10:38.862 | INFO | PID:216 | model_file_save.start_actor_process_by_type:776 - learner model_file_save process start, type is 0, no need get mode file from cos
2025-07-17 22:10:38.889 | INFO | PID:289 | monitor_proxy_process.before_run:73 - learner monitor_proxy process start success at pid is 289
2025-07-17 22:10:39.566 | INFO | PID:216 | model_wrapper_common.create_standard_model_wrapper:99 - learner policy_name train_one, algo diy, model_wrapper is StandardModelWrapperPytorch
2025-07-17 22:10:39.587 | INFO | PID:291 | model_file_sync.before_run:189 - learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch
2025-07-17 22:10:39.588 | INFO | PID:291 | model_file_sync.before_run:195 - model_file_sync process pid is 291
2025-07-17 22:10:39.590 | INFO | PID:291 | model_file_sync.make_model_dirs:153 - model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/convert_models_learner success
2025-07-17 22:10:39.591 | INFO | PID:291 | monitor_proxy.__init__:36 - learner ppid is 140681992238912
2025-07-17 22:10:39.591 | INFO | PID:216 | on_policy_trainer.before_run:674 - learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv
2025-07-17 22:10:39.597 | INFO | PID:292 | monitor_proxy.__init__:36 - learner ppid is 140681992238912
2025-07-17 22:10:39.600 | INFO | PID:216 | agent.save_model:189 - learner save model /data/ckpt/gorge_walk_v2_diy/model.ckpt-0.pth successfully
2025-07-17 22:10:39.600 | INFO | PID:292 | model_file_save.before_run:300 - model_file_save process start success at pid 292
2025-07-17 22:10:39.632 | INFO | PID:216 | model_file_sync.push_checkpoint_to_model_pool:560 - model_file_sync push output_file_name /data/ckpt/gorge_walk_v2_diy/kaiwu_checkpoint_gorge_walk_v2_diy_0.tar.gz key model.ckpt_gorge_walk_v2_diy_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0
2025-07-17 22:10:39.634 | INFO | PID:216 | on_policy_trainer.before_run:706 - learner train first model file push to modelpool success
2025-07-17 22:10:39.635 | INFO | PID:216 | on_policy_trainer.before_run:739 - learner train process start success at 216, on-policy/off-policy is off-policy, diy trainer global step -1.0, load app gorge_walk_v2 algo diy model, train_batch_size is 256
2025-07-17 22:10:39.639 | INFO | PID:216 | reverb_dataset_v1.start_background_filler:56 - learner start_background_filler success, reverb.Client connect at localhost:9999
[reverb/cc/client.cc:165] Sampler and server are owned by the same process (216) so Table reverb_replay_buffer_table_0 is accessed directly without gRPC.
2025-07-17 22:12:39.671 | INFO | PID:291 | model_file_sync.push_checkpoint_to_model_pool:560 - model_file_sync push output_file_name /data/ckpt/gorge_walk_v2_diy/kaiwu_checkpoint_gorge_walk_v2_diy_0.tar.gz key model.ckpt_gorge_walk_v2_diy_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0
