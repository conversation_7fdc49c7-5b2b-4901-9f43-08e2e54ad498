#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from agent_diy.feature.definition import (
    sample_process,
    reward_shaping,
)
from agent_diy.agent import Agent
from kaiwu_agent.utils.common_func import Frame
from kaiwu_agent.utils.common_func import attached
from tools.train_env_conf_validate import read_usr_conf
from tools.metrics_utils import get_training_metrics
import kaiwu_env
import time
import math
import os
from agent_diy.conf.conf import Config


def check_loss_convergence(agent, logger):
    """
    检查损失是否收敛
    Check if loss has converged
    """
    if not hasattr(agent.algorithm, 'loss_history') or len(agent.algorithm.loss_history) < Config.LOSS_STABLE_COUNT:
        return False

    # 获取最近N次的损失值
    recent_losses = agent.algorithm.loss_history[-Config.LOSS_STABLE_COUNT:]

    # 检查是否所有损失都小于阈值
    all_below_threshold = all(loss < Config.LOSS_THRESHOLD for loss in recent_losses)

    if all_below_threshold:
        avg_recent_loss = sum(recent_losses) / len(recent_losses)
        logger.info(f"Loss converged: {Config.LOSS_STABLE_COUNT} consecutive losses below {Config.LOSS_THRESHOLD}")
        logger.info(f"Average recent loss: {avg_recent_loss:.6f}")
        return True

    return False


@attached
def workflow(envs, agents, logger=None, monitor=None):
    try:
        # Read and validate configuration file
        # 配置文件读取和校验
        usr_conf = read_usr_conf("agent_diy/conf/train_env_conf.toml", logger)
        if usr_conf is None:
            if logger:
                logger.error("usr_conf is None, please check agent_diy/conf/train_env_conf.toml")
            return

        env, agent = envs[0], agents[0]
        
        EPISODES = 200
        update_cnt = 0

        # Initializing monitoring data
        # 监控数据初始化
        monitor_data: dict[str, float] = {
            "reward": 0.0,
            "diy_1": 0.0, # win_cnt
            "diy_2": 0.0, # rollout_len
            "diy_3": 0.0, # loss
            "diy_4": 0.0, # lr
            "diy_5": 0.0,
        }
        last_report_monitor_time = time.time()
        if logger:
            logger.info("Start Training...")
        start_t = time.time()
        last_save_model_time = start_t

        total_rew, episode_cnt, win_cnt = (
            0,
            0,
            0,
        )
        episode = 0
        while episode < EPISODES:
            # 衰减lr
            frac = 1.0 - episode / EPISODES
            lrnow = frac * Config.LEARNING_RATE
            for param_group in agent.algorithm.optimizer.param_groups:
                param_group['lr'] = lrnow
            monitor_data['diy_4'] = lrnow
            

            # 获取训练中的指标
            training_metrics = get_training_metrics()
            if training_metrics:
                if logger:
                    logger.info(f"training_metrics is {training_metrics}")

            samples = []
            while len(samples) < 4000: #收集不少于xx条样本

                #agent.algorithm.net = agent.algorithm.net.to(Config.DEVICE) #bison:这里是不得已的补丁，因为agent.save_mode现在的代码，不知道为什么会影响到net，导致出现在cpu上

                # Reset the game and get the initial state
                # 重置游戏, 并获取初始状态
                obs, extra_info = env.reset(usr_conf=usr_conf)
                if extra_info["result_code"] != 0:
                    if logger:
                        logger.error(
                            f"env.reset result_code is {extra_info['result_code']}, result_message is {extra_info['result_message']}"
                        )
                        raise RuntimeError(extra_info["result_message"])

                # Feature processing
                # 特征处理
                obs_data = agent.observation_process(obs, extra_info)
                # Task loop
                # 任务循环
                done = False
                while not done:
                    # Agent performs inference to obtain the predicted action for the next frame
                    # Agent 进行推理, 获取下一帧的预测动作
                    act_data, model_version = agent.predict(list_obs_data=[obs_data])
                    act_data = act_data[0]

                    

                    # Unpacking ActData into actions
                    # ActData 解包成动作
                    act, logp, value = agent.action_process(act_data)

                    # Interact with the environment, perform actions, and obtain the next state
                    # 与环境交互, 执行动作, 获取下一步的状态
                    frame_no, _obs, terminated, truncated, _extra_info = env.step(act)
                    if _extra_info["result_code"] != 0:
                        if logger:
                            logger.error(
                                f"extra_info.result_code is {_extra_info['result_code']}, \
                                extra_info.result_message is {_extra_info['result_message']}"
                            )
                        break

                    #bison:调试一下exploit
                    #observation = {'obs':_obs, 'extra_info':_extra_info}
                    #agent.exploit(observation)

                    # Feature processing
                    # 特征处理
                    _obs_data = agent.observation_process(_obs, _extra_info)

                    # Compute reward
                    # 计算 reward
                    score = _extra_info["score_info"]["score"]
                    #放在obs里方便传入下面的函数里
                    obs['extra_info'] = extra_info
                    _obs['extra_info'] = _extra_info
                    reward = reward_shaping(frame_no, score, terminated, truncated, obs, _obs)

                    # Determine over and update the win count
                    # 判断结束, 并更新胜利次数
                    done = terminated or truncated
                    if terminated:
                        win_cnt += 1

                    # Updating data and generating frames for training
                    # 数据更新, 生成训练需要的 frame
                    sample = Frame(
                        state=obs_data.feature,
                        action=act,
                        reward=reward,
                        done=done, 
                        log_probs_old = logp,
                        value_old = value,
                        monitor=monitor_data,
                    )

                    # Sample processing
                    # 样本处理
                    sample = sample_process([sample])
                    samples.extend(sample)

                    # Update total reward and state
                    # 更新总奖励和状态
                    total_rew += reward
                    obs_data = _obs_data
                episode += 1
                episode_cnt += 1
            # train
            # 训练, 更新target qnet
            agent.learn(samples)
            update_cnt += 1

            # Reporting training progress
            # 上报训练进度
            now = time.time()

            # 检查损失停止条件
            is_loss_converged = check_loss_convergence(agent, logger)

            # 原有的胜率收敛条件
            is_win_rate_converged = win_cnt / (episode + 1) > 0.95 and episode > 100

            if is_loss_converged or is_win_rate_converged or now - last_report_monitor_time > 60:
                avg_reward = total_rew / episode_cnt
                current_loss = agent.algorithm.current_loss if agent.algorithm.current_loss is not None else 0.0
                if logger:
                    logger.info(f"Episode: {episode + 1}, Avg Reward: {avg_reward}, Current Loss: {current_loss:.6f}")
                    logger.info(f"Training Win Rate: {win_cnt / (episode + 1)}, {win_cnt}, {episode+1}")

                    # 输出损失历史统计
                    if len(agent.algorithm.loss_history) >= 10:
                        recent_10_losses = agent.algorithm.loss_history[-10:]
                        avg_recent_loss = sum(recent_10_losses) / len(recent_10_losses)
                        logger.info(f"Average loss (last 10): {avg_recent_loss:.6f}")

                monitor_data["reward"] = float(avg_reward)
                monitor_data['diy_1'] = float(win_cnt)
                if monitor:
                    monitor.put_data({os.getpid(): monitor_data})

                total_rew = 0
                episode_cnt = 0
                last_report_monitor_time = now

                # The model has converged, training is complete
                # 模型收敛, 结束训练
                if is_loss_converged:
                    if logger:
                        logger.info(f"Training Converged (Loss Stable) at Episode: {episode + 1}")
                    break
                elif is_win_rate_converged:
                    if logger:
                        logger.info(f"Training Converged (Win Rate) at Episode: {episode + 1}")
                    break

            # Saving the model every 5 minutes
            # 每5分钟保存一次模型
            if now - last_save_model_time > 300:
                if logger:
                    logger.info(f"Saving Model at Episode: {episode + 1}")
                agent.save_model()
                last_save_model_time = now

        end_t = time.time()
        if logger:
            logger.info(f"Training Time for {episode + 1} episodes: {end_t - start_t} s")
        agent.episodes = episode + 1

        # model saving
        # 保存模型
        agent.save_model()

    except Exception as e:
        raise RuntimeError(f"workflow error")
