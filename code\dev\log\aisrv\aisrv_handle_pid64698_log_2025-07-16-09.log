{"time": "2025-07-16 09:24:06.001272", "level": "INFO", "message": "aisrv aisrvhandle use kaiwu_rl_helper: <KaiWuRLS<PERSON>dardHelper(kaiwu_rl_helper_0, initial daemon)>", "file": "aisrv_server_standard.py", "line": "794", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 64698}
{"time": "2025-07-16 09:24:06.005526", "level": "INFO", "message": "aisrv aisrvhandle established connect to :5566, slot id is 0, min_slot_id is 0", "file": "aisrv_server_standard.py", "line": "797", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 64698}
{"time": "2025-07-16 09:24:06.010782", "level": "INFO", "message": "aisrv aisrvhandle current_actor_addrs is ['127.0.0.1:8888'], current_learner_addrs is ['127.0.0.1:9999']", "file": "aisrv_server_standard.py", "line": "806", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 64698}
{"time": "2025-07-16 09:24:06.013690", "level": "INFO", "message": "aisrv aisrvhandle start success at pid 64698", "file": "aisrv_server_standard.py", "line": "837", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 64698}
