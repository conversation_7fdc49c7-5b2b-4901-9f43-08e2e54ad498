#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import numpy as np
from typing import Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from agent_q_learning.model.model import DQNTrainer

try:
    from agent_q_learning.model.model import DQNTrainer
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    DQNTrainer = None  # type: ignore


class Algorithm:
    def __init__(self, gamma, learning_rate, state_size, action_size, use_dqn=False):
        self.gamma = gamma
        self.learning_rate = learning_rate
        self.state_size = state_size
        self.action_size = action_size
        self.use_dqn = use_dqn and TORCH_AVAILABLE

        if self.use_dqn and TORCH_AVAILABLE and DQNTrainer is not None:
            # Use Deep Q-Network
            # 使用深度Q网络
            self.dqn_trainer = DQNTrainer(
                state_shape=state_size,
                action_shape=action_size,
                learning_rate=learning_rate,
                gamma=gamma
            )
            self.Q: Optional[np.ndarray] = None  # Not used in DQN mode
        else:
            # Use traditional Q-table
            # 使用传统Q表
            self.Q = np.ones([self.state_size, self.action_size])
            self.dqn_trainer = None
            if use_dqn and not TORCH_AVAILABLE:
                print("Warning: PyTorch not available, falling back to Q-table mode")

    def learn(self, list_sample_data):
        """
        Update the Q-function with the given game data
        使用给定的游戏数据更新Q函数

        Supports both traditional Q-table and Deep Q-Network modes
        支持传统Q表和深度Q网络两种模式
        """
        sample = list_sample_data[0]
        state, action, reward, next_state, done = (
            sample.state,
            sample.action,
            sample.reward,
            sample.next_state,
            sample.done,
        )

        if self.use_dqn:
            # Deep Q-Network mode
            # 深度Q网络模式
            self._learn_dqn(state, action, reward, next_state, done)
        else:
            # Traditional Q-table mode
            # 传统Q表模式
            self._learn_qtable(state, action, reward, next_state, done)

        return

    def _learn_qtable(self, state, action, reward, next_state, done):
        """
        Traditional Q-table learning
        传统Q表学习
        """
        if self.Q is None:
            return

        if done:
            # Terminal state: no future reward
            # 终止状态：没有未来奖励
            target = reward
        else:
            # Non-terminal state: include discounted future reward
            # 非终止状态：包含折扣的未来奖励
            target = reward + self.gamma * np.max(self.Q[next_state, :])

        # Calculate TD error and update Q-value
        # 计算时序差分误差并更新Q值
        td_error = target - self.Q[state, action]
        self.Q[state, action] += self.learning_rate * td_error

    def _learn_dqn(self, state, action, reward, next_state, done):
        """
        Deep Q-Network learning with experience replay
        使用经验回放的深度Q网络学习
        """
        if self.dqn_trainer is None:
            return None

        # Convert state indices to feature vectors if needed
        # 如果需要，将状态索引转换为特征向量
        if isinstance(state, (int, np.integer)):
            state_vector = self._state_to_vector(state)
            next_state_vector = self._state_to_vector(next_state)
        else:
            state_vector = state
            next_state_vector = next_state

        # Add experience to replay buffer
        # 添加经验到回放缓冲区
        self.dqn_trainer.add_experience(state_vector, action, reward, next_state_vector, done)

        # Train the network
        # 训练网络
        loss = self.dqn_trainer.train_step()
        return loss

    def _state_to_vector(self, state_index):
        """
        Convert state index to one-hot vector for DQN
        将状态索引转换为DQN的独热向量
        """
        vector = np.zeros(self.state_size)
        if 0 <= state_index < self.state_size:
            vector[state_index] = 1.0
        return vector

    def get_action(self, state, epsilon=0.0):
        """
        Get action using epsilon-greedy policy
        使用ε-贪心策略获取动作
        """
        if self.use_dqn and self.dqn_trainer is not None:
            if isinstance(state, (int, np.integer)):
                state_vector = self._state_to_vector(state)
                return self.dqn_trainer.get_action(state_vector, epsilon)
            else:
                return self.dqn_trainer.get_action(state, epsilon)
        else:
            # Traditional Q-table epsilon-greedy
            # 传统Q表ε-贪心
            if self.Q is None:
                return np.random.randint(self.action_size)
            if np.random.random() < epsilon:
                return np.random.randint(self.action_size)
            else:
                return np.argmax(self.Q[state, :])

    def save_model(self, path):
        """
        Save the model (Q-table or DQN)
        保存模型（Q表或DQN）
        """
        if self.use_dqn and self.dqn_trainer is not None:
            self.dqn_trainer.save_model(path)
        elif self.Q is not None:
            np.save(path, self.Q)

    def load_model(self, path):
        """
        Load the model (Q-table or DQN)
        加载模型（Q表或DQN）
        """
        if self.use_dqn and self.dqn_trainer is not None:
            self.dqn_trainer.load_model(path)
        else:
            self.Q = np.load(path)
