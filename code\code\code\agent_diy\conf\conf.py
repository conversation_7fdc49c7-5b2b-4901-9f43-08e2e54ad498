#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

import torch


# Configuration of dimensions
# 关于维度的配置
class Config:
    LEARNING_RATE = 3e-4
    GAMMA = 0.99
    LAMBDA = 0.95
    DEVICE='cuda:0' if torch.cuda.is_available() else 'cpu'
    STATE_DIM = 238
    ACTION_DIM = 4
    PPO_EPOCHS = 6
    PPO_EPS=0.2
    entropy_coef = 0.01
    vf_coef = 0.5
    max_grad_norm=0.5
    batch_size = 128

    # 预训练模型配置 / Pre-trained model configuration
    LOAD_MODEL_ID = None              # 加载模型ID，不加载写None / Model ID to load, set to None to disable

    # 损失停止条件配置 / Loss-based stopping configuration
    LOSS_THRESHOLD = 0.001            # 损失阈值 / Loss threshold
    LOSS_STABLE_COUNT = 50            # 连续稳定次数 / Consecutive stable count

