#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""
from agent_diy.conf.conf import Config
from agent_diy.model.model import Model
import numpy as np
from torch.optim.adam import Adam
import torch
import torch.nn.functional  as F
import torch.nn as nn
from torch.nn.utils.clip_grad import clip_grad_norm_



class Algorithm:
    def __init__(self):
        self.gamma = Config.GAMMA
        self.learning_rate = Config.LEARNING_RATE
        self.device=Config.DEVICE


        self.state_dim = Config.STATE_DIM
        self.action_dim = Config.ACTION_DIM

        self.net = Model(self.state_dim, self.action_dim).to(self.device)
        self.optimizer = Adam(self.net.parameters(), lr=self.learning_rate)

        # 损失历史记录
        self.loss_history = []
        self.current_loss = None

    def compute_gae_returns(self, rewards, values, dones):

        values_with_bootstrap = values + [0]  #每次都是完整的一个episode结束，所以可以直接补0, 补几都可以，被done屏蔽掉
        gae = 0
        returns = []
        # 从后向前遍历计算 GAE 和 Returns
        for t in reversed(range(len(rewards))):
            # TD 误差
            # (1 - dones[t]) 用于在 episode 结束时截断未来回报
            delta = rewards[t] + self.gamma * values_with_bootstrap[t + 1] * (1 - int(dones[t])) - values_with_bootstrap[t]  
            # GAE 计算
            gae = delta + Config.GAMMA * Config.LAMBDA * (1 - int(dones[t])) * gae  #
            returns.insert(0, gae + values_with_bootstrap[t])  #

        # 转换为 PyTorch 张量
        returns = torch.tensor(returns, device=self.device)
        # 优势估计
        advantages = returns - torch.tensor(values, device=self.device)
        return returns, advantages


    def learn(self, list_sample_data):

        monitor_data = list_sample_data[0].monitor
     
        samples = [(s.state, s.action, s.reward,s.done, s.log_probs_old, s.value_old) for s in list_sample_data]
        states, actions, rewards, dones, log_probs_old, values_old = zip(*samples)

        # 计算回报和优势s
        all_returns, all_advantages = self.compute_gae_returns(list(rewards), list(values_old), list(dones) ) #
        # 优势归一化，通常有助于稳定训练
        all_advantages = (all_advantages - all_advantages.mean()) / (all_advantages.std() + 1e-8)  #

        # 将收集到的数据转换为 PyTorch 张量
        all_states = torch.FloatTensor(states).to(self.device)
        all_actions_raw = torch.tensor(actions, dtype=torch.int64).to(self.device)
        all_rewards = torch.FloatTensor(rewards).to(self.device)
        all_log_probs_old = torch.FloatTensor(log_probs_old).to(self.device)  #log_probs_old 是 (N,)
        all_dones = torch.IntTensor(dones).to(self.device)
        all_values_old = torch.FloatTensor(values_old).to(self.device) 

        total_steps = all_states.shape[0]
        batch_size = Config.batch_size

        # --- PPO 更新循环 ---
        for i in range(Config.PPO_EPOCHS):  # PPO_epochs = 10
            indices = torch.tensor(np.random.permutation(total_steps), device=self.device)  # 打乱数据并转换为tensor
            for mb in range(0, total_steps, batch_size):
                mb_indices = indices[mb:mb+batch_size]
                states = all_states[mb_indices]
                actions_raw = all_actions_raw[mb_indices]
                rewards = all_rewards[mb_indices]
                returns = all_returns[mb_indices]
                advantages = all_advantages[mb_indices]
                log_probs_old = all_log_probs_old[mb_indices]
                dones = all_dones[mb_indices]
                values_old = all_values_old[mb_indices]

                # 计算新策略下原始动作的对数概率\熵
                _, log_probs_new, entropy, value_preds = self.net.sample(states, actions_raw)  #
        
                # 计算 PPO 比例
                ratio = torch.exp(log_probs_new - log_probs_old)  #

                # PPO 策略损失的两个项
                surr1 = ratio * advantages  #
                surr2 = torch.clamp(ratio, 1.0 - Config.PPO_EPS, 1.0 + Config.PPO_EPS) * advantages  #

                # PPO 策略损失 (带熵正则化)
                policy_loss = -torch.min(surr1, surr2).mean() - Config.entropy_coef * entropy.mean()  #

                # 值函数损失 (MSE Loss)
                value_loss = F.mse_loss(value_preds, returns)  #

                loss = policy_loss + Config.vf_coef * value_loss

                # 网络更新
                self.optimizer.zero_grad()
                loss.backward()
                clip_grad_norm_(self.net.parameters(), Config.max_grad_norm)
                self.optimizer.step()

        # 记录损失值
        loss_value = loss.cpu().item()
        self.current_loss = loss_value
        self.loss_history.append(loss_value)

        # 保持损失历史长度不超过1000
        if len(self.loss_history) > 1000:
            self.loss_history = self.loss_history[-1000:]

        monitor_data['diy_3'] = loss_value
        monitor_data['diy_2'] = all_states.shape[0] #返回每次rollout的样本数
        return loss_value

