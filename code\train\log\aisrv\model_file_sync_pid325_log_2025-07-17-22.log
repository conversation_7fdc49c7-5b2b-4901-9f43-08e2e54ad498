{"time": "2025-07-17 22:10:44.573570", "level": "INFO", "message": "aisrv model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "aisrv", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 325}
{"time": "2025-07-17 22:10:44.575039", "level": "INFO", "message": "model_file_sync process pid is 325", "file": "model_file_sync.py", "line": "195", "module": "aisrv", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 325}
{"time": "2025-07-17 22:10:44.576240", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/plugins success", "file": "model_file_sync.py", "line": "138", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 325}
{"time": "2025-07-17 22:10:44.577183", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/models /data/ckpt/gorge_walk_v2_diy/plugins success", "file": "model_file_sync.py", "line": "144", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 325}
{"time": "2025-07-17 22:10:44.578674", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_diy/convert_models_aisrv success", "file": "model_file_sync.py", "line": "153", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 325}
{"time": "2025-07-17 22:12:44.633276", "level": "INFO", "message": "model_file_sync pull kaiwu_checkpoint_gorge_walk_v2_diy_0.tar.gz from modelpool to /data/ckpt/gorge_walk_v2_diy/models/model.ckpt_gorge_walk_v2_diy_0 success                                 total pull from modelpool succ cnt is 1                                 total pull from modelpool err cnt is 0", "file": "model_file_sync.py", "line": "902", "module": "aisrv", "process": "model_file_sync", "function": "pull_checkpoint_from_model_pool", "stack": "", "pid": 325}
