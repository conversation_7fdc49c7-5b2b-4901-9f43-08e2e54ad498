#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import numpy as np
from kaiwu_agent.agent.base_agent import (
    predict_wrapper,
    exploit_wrapper,
    learn_wrapper,
    save_model_wrapper,
    load_model_wrapper,
)
from kaiwu_agent.utils.common_func import create_cls, attached
from kaiwu_agent.agent.base_agent import BaseAgent
from agent_q_learning.conf.conf import Config
from agent_q_learning.algorithm.algorithm import Algorithm
import torch


ObsData = create_cls("ObsData", feature=None, img=None)
ActData = create_cls("ActData", act=None)


@attached
class Agent(BaseAgent):
    def __init__(self, agent_type="player", device=None, logger=None, monitor=None) -> None:
        self.logger = logger

        # Initialize parameters
        # 参数初始化
        self.state_size = Config.STATE_DIM
        self.action_size = Config.ACTION_DIM
        self.learning_rate = Config.LEARNING_RATE
        self.gamma = Config.GAMMA
        self.epsilon = Config.EPSILON
        self.episodes = Config.EPISODES
        self.algorithm = Algorithm(self.gamma, self.learning_rate)

        super().__init__(agent_type, device, logger, monitor)

    @predict_wrapper
    def predict(self, list_obs_data):
        """
        The input is list_obs_data, and the output is list_act_data.
        """
        """
        输入是 list_obs_data, 输出是 list_act_data
        """
        state, img = list_obs_data[0].feature,list_obs_data[0].img
        act = self._epsilon_greedy(state=state, epsilon=self.epsilon, img=img)

        return [ActData(act=act)]

    @exploit_wrapper
    def exploit(self, observation):
        obs_data = self.observation_process(observation["obs"], observation["extra_info"])
        state = obs_data.feature
        img = obs_data.img
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.algorithm.device)
        img_tensor = torch.FloatTensor(img).unsqueeze(0).to(self.algorithm.device)
        qvalues = self.algorithm.qnet(state_tensor, img_tensor) 
        action = qvalues.argmax(-1).cpu().item()
        act_data = ActData(act=action)
        act = self.action_process(act_data)
        return act

    def _epsilon_greedy(self, state, img, epsilon=0.1):
        """
        Epsilon-greedy algorithm for action selection
        """
        """
        ε-贪心算法用于动作选择
        """
        if np.random.rand() <= epsilon:
            action = np.random.randint(0, self.action_size)

        # Exploitation
        # 探索
        else:
    
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.algorithm.device)
            img_tensor = torch.FloatTensor(img).unsqueeze(0).to(self.algorithm.device)
            qvalues = self.algorithm.qnet(state_tensor, img_tensor) 
            action = qvalues.argmax(-1).cpu().item()

        return action

    @learn_wrapper
    def learn(self, list_sample_data):
        return self.algorithm.learn(list_sample_data)

    def observation_process(self, raw_obs, extra_info):

        ############################################################
        # 用CNN提取地图特征，通常这样的效果比MLP更好，有相互间的方位信息：
        # 1. 通道0：障碍物所在位置的地图，值取1表示障碍物品
        # 2. 通道1：英雄所在的位置的地图 ，值取1
        # 3. 通道2：终点所在位置的地图，值取1
        # 4. 通道3：宝箱位置的地图，值取1
        # 5. 通道4：局部视野范围

        game_info = extra_info["game_info"]
        assert game_info["view"] == 2, ""
        
        # 一张大地图，把英雄、宝箱等信息都在地图上标注出来，下标依次是c，y， x
        channels = np.zeros((5, Config.MAP_SIZE, Config.MAP_SIZE))
        #英雄的位置
        hero_pos = [game_info["pos_x"], game_info["pos_z"]]
        channels[1, hero_pos[1], hero_pos[0] ] = 1

        # 障碍物、宝箱、终点的位置，还有一个通道表示局部视野
        for x in range(5): # x的变化范围
            for y in range(5): # y的变化范围
                local_idx = x * 5 + y #局部视野中的拉平坐标
                stuff = game_info["local_view"][local_idx] #局部视野中观测到的值
                global_pos = [hero_pos[0]+x-2,  hero_pos[1]+y-2] #全局视野中的二维坐标 x,y

                channels[4, global_pos[1], global_pos[0]] = 1 # 标识局部视野区域
               
                if stuff == 0 : #障碍物
                    channels[0, global_pos[1], global_pos[0]] = 1
                elif stuff == 3: #终点
                    channels[2, global_pos[1], global_pos[0]] = 1
                elif stuff == 4: # 宝箱
                    channels[3, global_pos[1], global_pos[0]] = 1

        ######################################################
        # 拉平的特征也保留，例如英雄所在位置的one-hot编码什么的
        game_info = extra_info["game_info"]
        pos = [game_info["pos_x"], game_info["pos_z"]]
        # Feature #1: Current state of the agent (1-dimensional representation)
        # 特征#1: 智能体当前 state (1维表示)
        state = [int(pos[0] * 64 + pos[1])]
        # Feature #2: One-hot encoding of the agent's current position
        # 特征#2: 智能体当前位置信息的 one-hot 编码
        pos_row = [0] * 64
        pos_row[pos[0]] = 1
        pos_col = [0] * 64
        pos_col[pos[1]] = 1

        # Feature #3: Discretized distance of the agent's current position from the endpoint
        # 特征#3: 智能体当前位置相对于终点的距离(离散化)
        # Feature #4: Discretized distance of the agent's current position from the treasure
        # 特征#4: 智能体当前位置相对于宝箱的距离(离散化)
        #end_treasure_dists = raw_obs["feature"] # 这个字段是11个距离，依次为到终点和宝箱的距离，如果宝箱不存在，999
        # bison:999这个值太大了，估计对稳定收敛有影响，改为0，这样做对于一开始没有宝箱的环境应该是ok的
        #end_treasure_dists = [0 if x == 999 else x for x in end_treasure_dists]

        # Feature #5: Graph features generation (obstacle information, treasure information, endpoint information)
        # 特征#5: 图特征生成(障碍物信息, 宝箱信息, 终点信息)
        local_view = [game_info["local_view"][i : i + 5] for i in range(0, len(game_info["local_view"]), 5)]
        obstacle_map, treasure_map, end_map = [], [], []
        for sub_list in local_view:
            obstacle_map.append([1 if i == 0 else 0 for i in sub_list])
            treasure_map.append([1 if i == 4 else 0 for i in sub_list])
            end_map.append([1 if i == 3 else 0 for i in sub_list])

        # Feature #6: Conversion of graph features into vector features
        # 特征#6: 图特征转换为向量特征
        obstacle_flat, treasure_flat, end_flat = [], [], []
        for i in obstacle_map:
            obstacle_flat.extend(i)
        for i in treasure_map:
            treasure_flat.extend(i)
        for i in end_map:
            end_flat.extend(i)

        # Feature #7: Information of the map areas visited within the agent's current local view
        # 特征#7: 智能体当前局部视野中的走过的地图信息
        # bison:这里可以推断拉平的局部数据和全局坐标的关系，x坐标是粗粒度的变化，delta_x = [-2,2], 在x固定的时候，y坐标做细粒度的变化，delta_y=[-2,2]
        memory_flat = []
        for i in range(game_info["view"] * 2 + 1):
            idx_start = (pos[0] - game_info["view"] + i) * 64 + (pos[1] - game_info["view"])
            memory_flat.extend(game_info["location_memory"][idx_start : (idx_start + game_info["view"] * 2 + 1)])

        tmp_treasure_status = [x if x != 2 else 0 for x in game_info["treasure_status"]]

        # Feature#8 ,允许的动作也编码进去
        # raw_obs['legal_act'] 我看了一下，基本上都是0，1，2，3,可能环境没有真正去做每个状态下的合法动作的细致化
        assert len(raw_obs['legal_act'] ) == 4, "legal act"
        

        feature = np.concatenate(
            [
                #state, #这个值可能会很大, 而且和后面两个字段比较重复，干掉...
                pos_row,
                pos_col,
                #end_treasure_dists,
                obstacle_flat,
                treasure_flat,
                end_flat,
                memory_flat,
                tmp_treasure_status,
            ]
        ) # 长度238的np数组

        # bison:下面是什么鬼，去掉！
        '''pos = int(feature[0])
        treasure_status = [int(item) for item in feature[-10:]]
        state = 1024 * pos + sum([treasure_status[i] * (2**i) for i in range(10)])'''
        #bison hack
        channels = np.zeros( (1,1,1) )
        return ObsData(feature=feature, img=channels) 

    # bison: 这个函数挺奇怪的，与observation_process()的方向刚好相反
    def action_process(self, act_data):
        return act_data.act

    @save_model_wrapper
    def save_model(self, path=None, id="1"):
        # To save the model, it can consist of multiple files,
        # and it is important to ensure that each filename includes the "model.ckpt-id" field.
        # 保存模型, 可以是多个文件, 需要确保每个文件名里包括了model.ckpt-id字段
        model_file_path = f"{path}/model.ckpt-{str(id)}.npy"
        torch.save(self.algorithm.qnet, model_file_path)
        self.logger.info(f"save model {model_file_path} successfully")
        pass

    @load_model_wrapper
    def load_model(self, path=None, id="1"):
        # When loading the model, you can load multiple files,
        # and it is important to ensure that each filename matches the one used during the save_model process.
        # 加载模型, 可以加载多个文件, 注意每个文件名需要和save_model时保持一致
        model_file_path = f"{path}/model.ckpt-{str(id)}.npy"
        try:
            #self.algorithm.Q = np.load(model_file_path)
            self.algorithm.qnet = torch.load(model_file_path, weights_only=False)
            self.algorithm.target_qnet.load_state_dict(self.algorithm.qnet.state_dict())
            self.logger.info(f"load model {model_file_path} successfully")
        except FileNotFoundError:
            self.logger.info(f"File {model_file_path} not found")
            exit(1)
