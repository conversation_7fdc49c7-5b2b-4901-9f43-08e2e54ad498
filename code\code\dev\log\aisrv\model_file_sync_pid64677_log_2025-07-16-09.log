{"time": "2025-07-16 09:24:02.949008", "level": "INFO", "message": "aisrv model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "aisrv", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 64677}
{"time": "2025-07-16 09:24:02.952908", "level": "INFO", "message": "model_file_sync process pid is 64677", "file": "model_file_sync.py", "line": "195", "module": "aisrv", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 64677}
{"time": "2025-07-16 09:24:02.954328", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_q_learning/plugins success", "file": "model_file_sync.py", "line": "138", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 64677}
{"time": "2025-07-16 09:24:02.955136", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_q_learning/models /data/ckpt/gorge_walk_v2_q_learning/plugins success", "file": "model_file_sync.py", "line": "144", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 64677}
{"time": "2025-07-16 09:24:02.956337", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/gorge_walk_v2_q_learning/convert_models_aisrv success", "file": "model_file_sync.py", "line": "153", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 64677}
