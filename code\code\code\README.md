# 强化学习算法练习项目 🎮


## 🚀 快速开始


1. **选择你想要的算法**：打开 `train_test.py` 文件，找到第40行：
   ```python
   algorithm_name = "diy"  # 改成你想要的算法名字
   ```

2. **运行训练**：
   ```bash
   python train_test.py
   ```

## 📁 项目结构

```
├── agent_diy/              # DIY算法
├── agent_q_learning/       # 经典Q-Learning
├── agent_dynamic_programming/  # 动态规划
├── agent_monte_carlo/      # 蒙特卡洛方法
├── agent_sarsa/           #  srasa
├── train_test.py          #  主程序
└── conf/                  # 配置文件
```

每个算法目录都包含：
- `agent.py` - 智能体主类
- `algorithm/` - 核心算法实现
- `model/` - 神经网络模型（如果使用）
- `conf/` - 算法配置

## 🛠️ 使用技巧

### 配置docker
docker-compose -f .docker-compose.yaml up -d
1