[app]
# === === === === === === === === === === === === === === === === === === === ===
# Below configurations can be modified as needed by users
# 下面的配置使用者可以按照需要修改
# === === === === === === === === === === === === === === === === === === === ===

# Enable learner to train in a continuous loop with sleep intervals to balance sample production/consumption
# learner执行while True循环的进行训练，设置休息时间以便设置样本生产消耗比
learner_train_by_while_true = true    # 启用连续训练循环
learner_train_sleep_seconds = 20  # 每次训练循环的休眠时间(秒)

# Replay buffer configurations
# 下面是replay buffer的相关配置
replay_buffer_capacity = 50  # 经验回放缓冲区容量
preload_ratio = 2               # 预加载比例

# Input dimension for reverb samples on learner (varies by algorithm)
# learner上reverb样本的输入维度, 注意不同的算法维度不一样
# 更新后的PPO特征维度: 26(特征) + 1(奖励) + 1(价值) + 1(td_return) + 1(优势) + 1(动作) + 1(概率) + 8(合法动作) = 40
sample_dim = 40

# Default number of threads for PyTorch (important for CPU resource control)
# torch使用时默认的线程数目, 针对单机限制CPU使用很重要
torch_num_threads = 1  # PyTorch使用的线程数

# Batch size for actor prediction
# actor预测批处理大小
predict_batch_size = 1    # Actor预测时的批次大小

# Sample batch size sent by aisrv in single episode
# aisrv在单局里多次发送样本大小配置
send_sample_size = 320    # 单局发送的样本数量

# Training batch size limit for learner
# learner训练批处理大小限制
train_batch_size = 20   # 训练时的批次大小

# Sample production/consumption ratio
# 样本消耗/生成采样比
production_consume_ratio = 2  # 样本生产消费比例

# Reverb removal policy options: Fifo, Lifo, Prioritized
# reverb移除策略, 可选项是reverb.selectors.Lifo, reverb.selectors.Prioritized, reverb.selectors.Fifo
reverb_remover = "reverb.selectors.Fifo"    # 移除策略：先进先出

# Reverb sampling policy options: Uniform, Fifo, Prioritized
# reverb采样策略, 可选项是reverb.selectors.Prioritized, reverb.selectors.Fifo, reverb.selectors.Uniform
reverb_sampler = "reverb.selectors.Uniform" # 采样策略：均匀采样

# Preload model configurations
# 预加载模型文件夹路径和ID
preload_model = false                                    # 是否预加载模型
preload_model_dir = "/data/ckpt/back_to_the_realm_v2_dqn/"  # 预加载模型路径
preload_model_id = 1000                                  # 预加载模型ID
dump_model_freq = 3                                    # 模型保存频率(步数)

# Evaluation model configurations
# 评估模式模型文件夹路径和ID
eval_model_dir = "/data/ckpt/back_to_the_realm_v2_dqn/"
eval_model_id = 0

# === === === === === === === === === === === === === === === === === === === ===
# Below configurations are framework-related, not recommended for modification
# 下面的配置, 由框架使用, 不建议使用者修改
# === === === === === === === === === === === === === === === === === === === ===

# Application specific configurations
# 下面的项目是每个app要单独配置的
app = "back_to_the_realm_v2"
self_play = false
set_name = "back_to_the_realm_v2_set1000"
self_play_set_name = "back_to_the_realm_v2_set1000"
selfplay_app_conf = "conf/app_conf_back_to_the_realm_v2.toml"
noselfplay_app_conf = "conf/app_conf_back_to_the_realm_v2.toml"
algo_conf = "conf/algo_conf_back_to_the_realm_v2.toml"
rainbow_env_name = "back_to_the_realm_v2_dev"

# Runtime mode (train/eval)
# 训练或者评估模式
run_mode = "train"

# Algorithm selection
# 采用的算法
algo = "ppo"

# Model synchronization interval (minutes)
# learner/actor之间同步model文件的时间间隔
model_file_sync_per_minutes = 2

# Model loading interval (minutes)
# actor加载model文件的时间间隔
model_file_load_per_minutes = 3

# Deep learning framework selection
# 使用的强化学习框架
use_which_deep_learning_framework = "pytorch"

# Prediction location strategy
# 预测是放在actor远程还是aisrv本地
predict_local_or_remote = "local"

# Framework integration pattern
# 接入采用标准化模式
framework_integration_patterns = "standard"

# AISRV framework type
# 采用接入KaiwuEnv方式
aisrv_framework = "kaiwu_env"

# Wrapper type options
# 采用的wrapper形式
wrapper_type = "remote"

# Directories to include in model saving
# 模型文件保存时需要包含的目录
copy_dir = "conf,agent_target_dqn,agent_dqn,agent_diy,agent_ppo"

# AISRV connection configuration
# 单个aisrv连接的kaiwu_env的数目
aisrv_connect_to_kaiwu_env_count = 8

# Model pool capacity
# model文件FIFO的个数
modelpool_max_save_model_count = 1

# Logging configurations
# 下面是日志文件相关配置
log_dir = "/data/projects/back_to_the_realm_v2/log"
level = "INFO"
tensorflow_log_level = "INFO"

# Network addresses configuration
# 设置actor和learner地址
actor_addrs = { train_one = ["127.0.0.1:8888"]}
learner_addrs = {train_one = ["127.0.0.1:9999"]}
