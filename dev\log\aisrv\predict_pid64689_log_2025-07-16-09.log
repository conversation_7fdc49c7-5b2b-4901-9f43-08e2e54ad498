{"time": "2025-07-16 09:24:04.818607", "level": "INFO", "message": "aisrv policy_name train_one, algo q_learning, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "aisrv", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 64689}
{"time": "2025-07-16 09:24:04.830859", "level": "INFO", "message": "aisrv predict just no model_pool files or copy_model_files failed, please check", "file": "multi_model_common.py", "line": "240", "module": "aisrv", "process": "multi_model_common", "function": "init_load_models", "stack": "", "pid": 64689}
{"time": "2025-07-16 09:24:04.842144", "level": "INFO", "message": "predict policy_name: train_one, start success at pid 64689, on-policy/off-policy is off-policy, actor_receive_cost_time_ms: 1, predict_batch_size: 1", "file": "actor_proxy_local.py", "line": "663", "module": "aisrv", "process": "actor_proxy_local", "function": "before_run", "stack": "", "pid": 64689}
{"time": "2025-07-16 09:24:04.845483", "level": "INFO", "message": "aisrv predict now predict count is 0", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 64689}
{"time": "2025-07-16 09:24:14.484813", "level": "INFO", "message": "aisrv monitor_proxy process start success at pid is 64761", "file": "monitor_proxy_process.py", "line": "73", "module": "aisrv", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 64761}
