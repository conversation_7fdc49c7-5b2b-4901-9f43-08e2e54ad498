{"time": "2025-07-16 09:24:20.674802", "level": "INFO", "message": "aisrv learner_proxy reverb client Client, server_address=127.0.0.1:9999 127.0.0.1:9999 connect to reverb server", "file": "reverb_util.py", "line": "22", "module": "aisrv", "process": "reverb_util", "function": "__init__", "stack": "", "pid": 64692}
{"time": "2025-07-16 09:24:20.676928", "level": "INFO", "message": "learner_proxy send reverb server use reverb, tables is ['reverb_replay_buffer_table_0']", "file": "learner_proxy.py", "line": "118", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 64692}
{"time": "2025-07-16 09:24:20.681136", "level": "INFO", "message": "learner_proxy zmq client connect at 127.0.0.1:9997 with client_id 44560661", "file": "learner_proxy.py", "line": "155", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 64692}
{"time": "2025-07-16 09:24:20.737774", "level": "INFO", "message": "learner_proxy policy_name: train_one, start success at pid 64692", "file": "learner_proxy.py", "line": "174", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 64692}
