#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import torch
import numpy as np
from torch import nn
import torch.nn.functional as F
from kaiwu_agent.utils.common_func import attached
from agent_q_learning.conf.conf import Config


class Model(nn.Module):
    def __init__(self, state_shape, action_shape):
        super().__init__()
        self.feat_sz1 = 128
        self.feat_sz2 = 128

        self.simple_net = nn.Sequential(
                nn.Linear(state_shape, 256),
                nn.<PERSON>L<PERSON>(),
                nn.Linear(256, self.feat_sz1),
                nn.ReLU(),
            )
        self.img_net = nn.Sequential(
                nn.Conv2d(5, 32, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.Conv2d(32, 8, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.Flatten(),
                nn.Linear(Config.MAP_SIZE*Config.MAP_SIZE*8, self.feat_sz2),
                nn.ReLU(),
        )
        self.fc = nn.Linear(self.feat_sz1, action_shape)


    def forward(self, feat, img):
        x1 = self.simple_net(feat)
        #x2 = self.img_net(img)
        #x3 = torch.concat([x1, x2], dim=1)
        x = self.fc(x1)
        return x

        # User-defined network
        # 用户自定义网络
